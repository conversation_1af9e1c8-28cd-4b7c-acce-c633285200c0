-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create tables
CREATE TABLE IF NOT EXISTS public.prompts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    category TEXT NOT NULL DEFAULT 'general',
    tags TEXT[] DEFAULT '{}',
    rating INTEGER DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
    is_pinned BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

CREATE TABLE IF NOT EXISTS public.folders (
    id UUID DEFAULT gen_random_uuid() PRIMARY <PERSON>E<PERSON>,
    name TEXT NOT NULL,
    color TEXT NOT NULL DEFAULT 'bg-gray-500',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL
);

CREATE TABLE IF NOT EXISTS public.prompt_folders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    prompt_id UUID REFERENCES public.prompts(id) ON DELETE CASCADE NOT NULL,
    folder_id UUID REFERENCES public.folders(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(prompt_id, folder_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_prompts_user_id ON public.prompts(user_id);
CREATE INDEX IF NOT EXISTS idx_prompts_category ON public.prompts(category);
CREATE INDEX IF NOT EXISTS idx_prompts_created_at ON public.prompts(created_at);
CREATE INDEX IF NOT EXISTS idx_prompts_is_pinned ON public.prompts(is_pinned);
CREATE INDEX IF NOT EXISTS idx_folders_user_id ON public.folders(user_id);
CREATE INDEX IF NOT EXISTS idx_prompt_folders_prompt_id ON public.prompt_folders(prompt_id);
CREATE INDEX IF NOT EXISTS idx_prompt_folders_folder_id ON public.prompt_folders(folder_id);

-- Enable Row Level Security
ALTER TABLE public.prompts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.prompt_folders ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for prompts
CREATE POLICY "Users can view their own prompts" ON public.prompts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own prompts" ON public.prompts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own prompts" ON public.prompts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own prompts" ON public.prompts
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for folders
CREATE POLICY "Users can view their own folders" ON public.folders
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own folders" ON public.folders
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own folders" ON public.folders
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own folders" ON public.folders
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for prompt_folders
CREATE POLICY "Users can view their own prompt_folders" ON public.prompt_folders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.prompts 
            WHERE prompts.id = prompt_folders.prompt_id 
            AND prompts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own prompt_folders" ON public.prompt_folders
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.prompts 
            WHERE prompts.id = prompt_folders.prompt_id 
            AND prompts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own prompt_folders" ON public.prompt_folders
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.prompts 
            WHERE prompts.id = prompt_folders.prompt_id 
            AND prompts.user_id = auth.uid()
        )
    );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_prompts_updated_at BEFORE UPDATE ON public.prompts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_folders_updated_at BEFORE UPDATE ON public.folders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data (optional)
-- Note: This will only work after a user is authenticated
-- You can run this manually after creating your first user

-- Sample prompts
-- INSERT INTO public.prompts (title, content, category, tags, rating, is_pinned, user_id) VALUES
-- ('Code Review Assistant', 'You are an expert code reviewer. Please review the following code and provide constructive feedback on code quality, best practices, potential bugs, and suggestions for improvement.', 'development', ARRAY['code', 'review', 'programming'], 5, true, auth.uid()),
-- ('Email Writer', 'Help me write a professional email for the following situation. Please ensure the tone is appropriate, the message is clear, and the format is professional.', 'communication', ARRAY['email', 'professional', 'writing'], 4, false, auth.uid()),
-- ('Data Analysis Helper', 'Analyze the following dataset and provide insights. Look for patterns, trends, anomalies, and provide actionable recommendations based on the data.', 'analysis', ARRAY['data', 'analysis', 'insights'], 5, true, auth.uid());

-- Sample folders
-- INSERT INTO public.folders (name, color, user_id) VALUES
-- ('Development', 'bg-blue-500', auth.uid()),
-- ('Communication', 'bg-green-500', auth.uid()),
-- ('Analysis', 'bg-purple-500', auth.uid()),
-- ('Creative', 'bg-orange-500', auth.uid());
