# Testing Guide for Prompt Manager

This guide will help you test all the functionality of the Prompt Manager application.

## Prerequisites

1. ✅ Supabase project is set up with your credentials
2. ✅ Database schema is created (run the SQL from SUPABASE_SETUP.md)
3. ✅ Development server is running (`pnpm dev`)
4. ✅ App is accessible at http://localhost:3000

## Test Checklist

### 1. Authentication Testing

#### Sign Up Flow
- [ ] Navigate to http://localhost:3000
- [ ] You should be redirected to `/auth/login`
- [ ] Click "Don't have an account? Sign up"
- [ ] Enter a valid email and password
- [ ] Click "Sign Up"
- [ ] Check your email for confirmation (if email confirmation is enabled)
- [ ] Verify you can sign up successfully

#### Sign In Flow
- [ ] Enter your email and password
- [ ] Click "Sign In"
- [ ] Verify you're redirected to the main app
- [ ] Check that your email appears in the top navigation

#### Sign Out Flow
- [ ] Click the "Logout" button in the top navigation
- [ ] Verify you're redirected back to the login page

### 2. Prompt Management Testing

#### Create Prompts
- [ ] Sign in to the app
- [ ] Click "+ New Prompt" in the sidebar
- [ ] Fill in the form:
  - Title: "Test Prompt"
  - Content: "This is a test prompt for validation"
  - Category: "Development"
- [ ] Click "Create Prompt"
- [ ] Verify the prompt appears in the main grid
- [ ] Check the activity log shows "created prompt"

#### View Prompts
- [ ] Verify prompts are displayed in a grid layout
- [ ] Check that each prompt shows:
  - Title
  - Content preview
  - Tags (if any)
  - Rating stars
  - Pin status
  - Creation date
  - Last used date (if applicable)

#### Copy Prompts
- [ ] Click the copy button on a prompt
- [ ] Verify the content is copied to clipboard
- [ ] Check the activity log shows "copied [prompt title]"
- [ ] Verify the "last used" date is updated

#### Pin/Unpin Prompts
- [ ] Click the pin button on a prompt
- [ ] Verify the pin icon appears/disappears
- [ ] Check the activity log shows "pinned/unpinned [prompt title]"

#### Search and Filter
- [ ] Use the search box to search for prompts
- [ ] Try searching by:
  - Title
  - Content
  - Tags
- [ ] Use the category dropdown to filter prompts
- [ ] Verify filtering works correctly

### 3. Folder Management Testing

#### View Folders
- [ ] Check the sidebar shows folders
- [ ] Verify each folder displays:
  - Name
  - Color indicator
  - Prompt count

#### Create Folders (if implemented)
- [ ] Click "+ New Folder" in the sidebar
- [ ] Create a new folder
- [ ] Verify it appears in the folder list

### 4. Real-time Testing

#### Multiple Browser Windows
- [ ] Open the app in two different browser windows
- [ ] Sign in with the same account in both
- [ ] Create a prompt in one window
- [ ] Verify it appears in the other window automatically
- [ ] Check the activity log shows real-time updates

### 5. Data Persistence Testing

#### Refresh Test
- [ ] Create some prompts
- [ ] Refresh the page
- [ ] Verify all prompts are still there
- [ ] Check that the data persists

#### Sign Out/In Test
- [ ] Create some prompts
- [ ] Sign out
- [ ] Sign back in
- [ ] Verify all your prompts are still there

### 6. Security Testing

#### User Isolation
- [ ] Create an account and add some prompts
- [ ] Sign out and create a different account
- [ ] Verify you can't see the first user's prompts
- [ ] Sign back in as the first user
- [ ] Verify your prompts are still there

### 7. Error Handling Testing

#### Network Issues
- [ ] Disconnect your internet
- [ ] Try to create a prompt
- [ ] Verify appropriate error messages appear
- [ ] Reconnect and verify the app recovers

#### Invalid Data
- [ ] Try to create a prompt with empty title
- [ ] Try to create a prompt with empty content
- [ ] Verify validation prevents submission

### 8. UI/UX Testing

#### Responsive Design
- [ ] Test the app on different screen sizes
- [ ] Verify the layout adapts properly
- [ ] Check mobile responsiveness

#### Loading States
- [ ] Verify loading spinners appear during data fetching
- [ ] Check that buttons show "Loading..." states during operations

#### Terminal Theme
- [ ] Verify the terminal-style design is consistent
- [ ] Check that the green color scheme is applied
- [ ] Verify the monospace font is used

## Expected Behavior

### Activity Log
The activity log should show:
- System initialization messages
- User actions (create, copy, pin, etc.)
- Real-time updates
- Error messages

### Performance
- Initial load should be fast
- Prompt creation should be near-instantaneous
- Real-time updates should appear within 1-2 seconds

### Data Integrity
- All user data should be properly isolated
- Prompts should maintain their properties (title, content, category, etc.)
- Timestamps should be accurate

## Troubleshooting

### Common Issues

1. **"Invalid URL" Error**
   - Check your `.env.local` file has correct Supabase credentials
   - Restart the development server

2. **"Row Level Security policy violation"**
   - Ensure you've run the database schema SQL
   - Check that RLS policies are created

3. **Authentication not working**
   - Verify Supabase project is active
   - Check authentication settings in Supabase dashboard

4. **Real-time updates not working**
   - Check browser console for WebSocket errors
   - Verify Supabase project has real-time enabled

### Debug Steps

1. Check browser console for errors
2. Check network tab for failed requests
3. Verify Supabase dashboard shows your data
4. Check the activity log for error messages

## Success Criteria

✅ **Authentication**: Users can sign up, sign in, and sign out
✅ **CRUD Operations**: Users can create, read, update, and delete prompts
✅ **Data Security**: Users can only see their own data
✅ **Real-time Updates**: Changes appear across multiple sessions
✅ **Persistence**: Data survives page refreshes and sign out/in cycles
✅ **Error Handling**: Appropriate error messages for edge cases
✅ **UI/UX**: Terminal-style interface is functional and responsive

If all tests pass, your Prompt Manager is ready for production deployment!
