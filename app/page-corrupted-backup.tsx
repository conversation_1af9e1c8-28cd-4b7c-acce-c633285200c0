"use client"

import { useState, useEffect, useMemo } from "react"
import { useRouter } from "next/navigation"
import { Search, Star, Copy, Edit, Pin, Clock, Terminal, LogOut, Folder, Trash2, Grid3X3, Grid2X2, LayoutGrid, List } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { promptService, folderService, promptFolderService, subscriptions } from "@/lib/database"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Prompt {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  rating: number
  is_pinned: boolean
  created_at: string
  updated_at: string
  last_used: string | null
  user_id: string
}

interface FolderType {
  id: string
  name: string
  color: string
  created_at: string
  updated_at: string
  user_id: string
  promptCount?: number
}

export default function TerminalPromptManager() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()

  // State declarations
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [folders, setFolders] = useState<FolderType[]>([])
  const [loadingPrompts, setLoadingPrompts] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [sortBy, setSortBy] = useState("smart")
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [layoutMode, setLayoutMode] = useState<'large' | 'medium' | 'small' | 'list'>('medium')
  
  // Dialog states
  const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = useState(false)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  
  // Form states
  const [newPrompt, setNewPrompt] = useState({ title: "", content: "", category: "general", tags: "", folder: "" })
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null)
  const [newFolder, setNewFolder] = useState({ name: "", color: "bg-blue-600" })
  
  // Drag and drop states
  const [draggedPrompt, setDraggedPrompt] = useState<Prompt | null>(null)
  const [dragOverFolder, setDragOverFolder] = useState<string | null>(null)
  const [dragOverTrash, setDragOverTrash] = useState(false)
  const [isTrashOpen, setIsTrashOpen] = useState(false)
  
  // Activity and settings
  const [activityLog, setActivityLog] = useState<string[]>(["System initialized"])
  const [userSettings, setUserSettings] = useState({
    customCategories: [] as string[],
    defaultSort: "smart" as string,
    defaultLayout: "medium" as string
  })
  const [newCustomCategory, setNewCustomCategory] = useState("")

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 p-8 text-center">
          <div className="text-green-400 text-lg mb-4">Loading...</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"></div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!user) {
    return null
  }

  // Helper functions
  const addToLog = (message: string) => {
    setActivityLog(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const allCategories = [
    "general", "development", "communication", "analysis", "creative",
    ...userSettings.customCategories
  ]

  // Load data on component mount
  useEffect(() => {
    if (user) {
      loadPrompts()
      loadFolders()
      loadSettings()
    }
  }, [user])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user) return

    const promptsSubscription = subscriptions.prompts(user.id, (payload) => {
      if (payload.eventType === 'INSERT') {
        setPrompts(prev => [...prev, payload.new as Prompt])
        addToLog("new prompt added")
      } else if (payload.eventType === 'UPDATE') {
        setPrompts(prev => prev.map(p => p.id === payload.new.id ? payload.new as Prompt : p))
        addToLog("prompt updated")
      } else if (payload.eventType === 'DELETE') {
        setPrompts(prev => prev.filter(p => p.id !== payload.old.id))
        addToLog("prompt deleted")
      }
    })

    const foldersSubscription = subscriptions.folders(user.id, (payload) => {
      if (payload.eventType === 'INSERT') {
        setFolders(prev => [...prev, payload.new as FolderType])
        addToLog("new folder created")
      } else if (payload.eventType === 'UPDATE') {
        setFolders(prev => prev.map(f => f.id === payload.new.id ? payload.new as FolderType : f))
        addToLog("folder updated")
      } else if (payload.eventType === 'DELETE') {
        setFolders(prev => prev.filter(f => f.id !== payload.old.id))
        addToLog("folder deleted")
      }
    })

    return () => {
      promptsSubscription?.unsubscribe()
      foldersSubscription?.unsubscribe()
    }
  }, [user])

  const loadSettings = () => {
    try {
      const saved = localStorage.getItem('promptManagerSettings')
      if (saved) {
        const settings = JSON.parse(saved)
        setUserSettings(settings)
        setSortBy(settings.defaultSort || "smart")
        setLayoutMode(settings.defaultLayout || "medium")
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const loadPrompts = async () => {
    if (!user) return
    
    try {
      setLoadingPrompts(true)
      const data = await promptService.getAll(user.id)
      setPrompts(data)
      addToLog(`loaded ${data.length} prompts`)
    } catch (error) {
      console.error('Error loading prompts:', error)
      addToLog("error loading prompts")
    } finally {
      setLoadingPrompts(false)
    }
  }

  const loadFolders = async () => {
    if (!user) return
    
    try {
      const data = await folderService.getAll(user.id)
      
      // Get prompt counts for each folder
      const foldersWithCounts = await Promise.all(
        data.map(async (folder) => {
          const promptCount = await promptFolderService.getPromptCountByFolder(folder.id)
          return { ...folder, promptCount }
        })
      )
      
      setFolders(foldersWithCounts)
      addToLog(`loaded ${data.length} folders`)
    } catch (error) {
      console.error('Error loading folders:', error)
      addToLog("error loading folders")
    }
  }

  // Database test function
  const testDatabase = async () => {
    try {
      addToLog("testing database connection...")
      await promptService.getAll(user!.id)
      addToLog("database connection successful")
    } catch (error) {
      console.error('Database test failed:', error)
      addToLog("database connection failed")
    }
  }

  // CRUD Functions
  const createPrompt = async () => {
    if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return

    try {
      const promptData = {
        title: newPrompt.title.trim(),
        content: newPrompt.content.trim(),
        category: newPrompt.category,
        tags: newPrompt.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        rating: 0,
        is_pinned: false,
        user_id: user.id
      }

      const createdPrompt = await promptService.create(promptData)

      // Add to folder if specified
      if (newPrompt.folder) {
        await promptFolderService.addPromptToFolder(createdPrompt.id, newPrompt.folder)
      }

      setNewPrompt({ title: "", content: "", category: "general", tags: "", folder: "" })
      setIsNewPromptDialogOpen(false)
      addToLog("prompt created successfully")
    } catch (error) {
      console.error('Error creating prompt:', error)
      addToLog("error creating prompt")
    }
  }

  const updatePrompt = async () => {
    if (!user || !editingPrompt) return

    try {
      const updatedData = {
        title: editingPrompt.title.trim(),
        content: editingPrompt.content.trim(),
        category: editingPrompt.category,
        tags: editingPrompt.tags,
        updated_at: new Date().toISOString()
      }

      await promptService.update(editingPrompt.id, updatedData, user.id)
      setEditingPrompt(null)
      setIsEditDialogOpen(false)
      addToLog("prompt updated successfully")
    } catch (error) {
      console.error('Error updating prompt:', error)
      addToLog("error updating prompt")
    }
  }

  const deletePrompt = async (id: string) => {
    if (!user) return

    try {
      await promptService.delete(id, user.id)
      addToLog("prompt deleted successfully")
    } catch (error) {
      console.error('Error deleting prompt:', error)
      addToLog("error deleting prompt")
    }
  }

  const updateRating = async (id: string, rating: number) => {
    if (!user) return

    try {
      await promptService.update(id, { rating }, user.id)
      addToLog(`rating updated to ${rating} stars`)
    } catch (error) {
      console.error('Error updating rating:', error)
      addToLog("error updating rating")
    }
  }

  const togglePin = async (id: string) => {
    if (!user) return

    try {
      await promptService.togglePin(id, user.id)
      addToLog("pin status toggled")
    } catch (error) {
      console.error('Error toggling pin:', error)
      addToLog("error toggling pin")
    }
  }

  const copyPrompt = async (prompt: Prompt) => {
    try {
      await navigator.clipboard.writeText(prompt.content)
      await promptService.updateLastUsed(prompt.id, user!.id)
      addToLog("prompt copied to clipboard")
    } catch (error) {
      console.error('Error copying prompt:', error)
      addToLog("error copying prompt")
    }
  }

  const openEditDialog = (prompt: Prompt) => {
    setEditingPrompt(prompt)
    setIsEditDialogOpen(true)
  }

  // Folder functions
  const createFolder = async () => {
    if (!user || !newFolder.name.trim()) return

    try {
      await folderService.create({
        name: newFolder.name.trim(),
        color: newFolder.color,
        user_id: user.id
      })

      setNewFolder({ name: "", color: "bg-blue-600" })
      setIsNewFolderDialogOpen(false)
      addToLog("folder created successfully")
    } catch (error) {
      console.error('Error creating folder:', error)
      addToLog("error creating folder")
    }
  }

  const movePromptToFolder = async (promptId: string, folderId: string | null) => {
    if (!user) return

    try {
      // Remove from current folder first
      await promptFolderService.removePromptFromAllFolders(promptId)

      // Add to new folder if specified
      if (folderId) {
        await promptFolderService.addPromptToFolder(promptId, folderId)
      }

      // Reload folders to update counts
      loadFolders()
      addToLog("prompt moved successfully")
    } catch (error) {
      console.error('Error moving prompt:', error)
      addToLog("error moving prompt")
    }
  }

  // Filtering and sorting logic
  const promptsWithFolderInfo = useMemo(() => {
    const filteredPrompts = prompts
      .filter(prompt => {
        // Search filter
        const matchesSearch = searchTerm === "" ||
          prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
          prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))

        // Category filter
        const matchesCategory = selectedCategory === "all" || prompt.category === selectedCategory

        // Folder filter
        let matchesFolder = true
        if (selectedFolder !== null) {
          // This would need to be enhanced with actual folder-prompt relationships
          // For now, we'll show all prompts when a folder is selected
          matchesFolder = true
        }

        return matchesSearch && matchesCategory && matchesFolder
      })
      .sort((a, b) => {
        if (sortBy === "smart") {
          // Smart sort: pinned first, then by rating, then by last used, then by date
          if (a.is_pinned !== b.is_pinned) {
            return a.is_pinned ? -1 : 1
          }
          if (a.rating !== b.rating) {
            return b.rating - a.rating
          }
          if (a.last_used && b.last_used) {
            return new Date(b.last_used).getTime() - new Date(a.last_used).getTime()
          }
          if (a.last_used && !b.last_used) return -1
          if (!a.last_used && b.last_used) return 1
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        } else if (sortBy === "rating") {
          if (a.is_pinned !== b.is_pinned) {
            return a.is_pinned ? -1 : 1
          }
          return b.rating - a.rating
        } else if (sortBy === "date") {
          if (a.is_pinned !== b.is_pinned) {
            return a.is_pinned ? -1 : 1
          }
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        } else if (sortBy === "title") {
          if (a.is_pinned !== b.is_pinned) {
            return a.is_pinned ? -1 : 1
          }
          return a.title.localeCompare(b.title)
        }
        return 0
      })

    // Add folder information to prompts for display
    return filteredPrompts.map(prompt => {
      // This would be enhanced with actual folder relationships
      return { ...prompt, folderInfo: null }
    })
  }, [prompts, searchTerm, selectedCategory, selectedFolder, sortBy])

  return (
    <div className="min-h-screen bg-gray-900 p-0">
      <div className="w-full h-screen">
        <div className="bg-gray-800 h-screen overflow-hidden">
          {/* Header */}
          <div className="bg-gray-700 px-6 py-3 border-b border-gray-600">
            <div className="flex items-center justify-between">
              <div className="text-green-400 font-bold text-lg">PROMPT MANAGER</div>
              <div className="flex items-center space-x-4">
                <div className="text-green-400 text-sm">{user?.email}</div>
                <Button
                  onClick={signOut}
                  variant="outline"
                  size="sm"
                  className="text-green-400 border-green-600 hover:bg-green-900"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </Button>
              </div>
            </div>
          </div>

          {/* Simple Content for Testing */}
          <div className="p-6">
            <div className="text-green-400 text-lg mb-4">Welcome to Prompt Manager!</div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {promptsWithFolderInfo.map((prompt) => (
                <Card key={prompt.id} className="bg-gray-900 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-green-400">{prompt.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-green-300 text-sm">{prompt.content}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="mt-4">
              <Button
                onClick={testDatabase}
                variant="outline"
                className="text-green-400 border-green-600 hover:bg-green-900"
              >
                Test Database Connection
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

          <div className="flex h-full">
            {/* Sidebar */}
            <div className="w-80 bg-gray-900 border-r border-gray-700 p-4 overflow-y-auto">
              {/* Folders Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-green-400 text-sm font-bold terminal-glow">&gt; FOLDERS</h3>
                  <Dialog open={isNewFolderDialogOpen} onOpenChange={setIsNewFolderDialogOpen}>
                    <DialogTrigger asChild>
                      <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                        +
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-gray-800 border-gray-600 text-green-300">
                      <DialogHeader>
                        <DialogTitle className="text-green-400">Create New Folder</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <Input
                          placeholder="Folder name"
                          value={newFolder.name}
                          onChange={(e) => setNewFolder(prev => ({ ...prev, name: e.target.value }))}
                          className="bg-gray-900 border-gray-600 text-green-300"
                        />
                        <Select value={newFolder.color} onValueChange={(value) => setNewFolder(prev => ({ ...prev, color: value }))}>
                          <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-800 border-gray-600">
                            <SelectItem value="bg-blue-600">Blue</SelectItem>
                            <SelectItem value="bg-green-600">Green</SelectItem>
                            <SelectItem value="bg-purple-600">Purple</SelectItem>
                            <SelectItem value="bg-red-600">Red</SelectItem>
                            <SelectItem value="bg-yellow-600">Yellow</SelectItem>
                            <SelectItem value="bg-pink-600">Pink</SelectItem>
                          </SelectContent>
                        </Select>
                        <div className="flex space-x-2">
                          <Button onClick={createFolder} className="flex-1 bg-green-600 hover:bg-green-700">
                            Create Folder
                          </Button>
                          <Button variant="outline" onClick={() => setIsNewFolderDialogOpen(false)} className="flex-1">
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>

                <div className="space-y-2">
                  {/* All Prompts */}
                  <div
                    className={`flex items-center p-2 rounded cursor-pointer transition-colors ${
                      selectedFolder === null ? 'bg-green-900/30 text-green-300' : 'text-green-500 hover:bg-gray-800'
                    }`}
                    onClick={() => setSelectedFolder(null)}
                  >
                    <Folder className="w-4 h-4 mr-2" />
                    <span className="flex-1">All Prompts</span>
                    <span className="text-xs">({prompts.length})</span>
                  </div>

                  {/* Individual Folders */}
                  {folders.map((folder) => (
                    <div
                      key={folder.id}
                      className={`flex items-center p-2 rounded cursor-pointer transition-colors ${
                        selectedFolder === folder.id ? 'bg-green-900/30 text-green-300' : 'text-green-500 hover:bg-gray-800'
                      }`}
                      onClick={() => setSelectedFolder(selectedFolder === folder.id ? null : folder.id)}
                    >
                      <div className={`w-4 h-4 ${folder.color} rounded mr-2`}></div>
                      <span className="flex-1 truncate">{folder.name}</span>
                      <span className="text-xs">({folder.promptCount || 0})</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Settings */}
              <div className="mb-6">
                <h3 className="text-green-400 text-sm font-bold mb-2 terminal-glow">&gt; SETTINGS</h3>
                <div className="space-y-1">
                  <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
                    <DialogTrigger asChild>
                      <button className="w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded">
                        Preferences
                      </button>
                    </DialogTrigger>
                    <DialogContent className="bg-gray-800 border-gray-600 text-green-300 max-w-md">
                      <DialogHeader>
                        <DialogTitle className="text-green-400">Settings</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-green-400 text-sm font-semibold mb-2">Custom Categories</h4>
                          <div className="flex space-x-2 mb-2">
                            <Input
                              placeholder="New category name"
                              value={newCustomCategory}
                              onChange={(e) => setNewCustomCategory(e.target.value)}
                              className="flex-1 bg-gray-900 border-gray-600 text-green-300"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  const category = newCustomCategory.trim().toLowerCase()
                                  if (category && !allCategories.includes(category)) {
                                    setUserSettings(prev => ({
                                      ...prev,
                                      customCategories: [...prev.customCategories, category]
                                    }))
                                    setNewCustomCategory("")
                                  }
                                }
                              }}
                            />
                            <Button
                              size="sm"
                              onClick={() => {
                                const category = newCustomCategory.trim().toLowerCase()
                                if (category && !allCategories.includes(category)) {
                                  setUserSettings(prev => ({
                                    ...prev,
                                    customCategories: [...prev.customCategories, category]
                                  }))
                                  setNewCustomCategory("")
                                }
                              }}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              Add
                            </Button>
                          </div>

                          {userSettings.customCategories.length > 0 && (
                            <div className="space-y-1">
                              <div className="text-xs text-green-500">Your custom categories:</div>
                              <div className="flex flex-wrap gap-1">
                                {userSettings.customCategories.map((category) => (
                                  <Badge
                                    key={category}
                                    variant="outline"
                                    className="text-xs text-green-400 border-green-500 bg-green-900/20 cursor-pointer hover:bg-red-900/20 hover:border-red-500 hover:text-red-400"
                                    onClick={() => {
                                      setUserSettings(prev => ({
                                        ...prev,
                                        customCategories: prev.customCategories.filter(c => c !== category)
                                      }))
                                    }}
                                  >
                                    {category.charAt(0).toUpperCase() + category.slice(1)} ×
                                  </Badge>
                                ))}
                              </div>
                              <div className="text-xs text-gray-500">Click to remove</div>
                            </div>
                          )}
                        </div>

                        <div className="border-t border-gray-700 pt-4">
                          <h4 className="text-green-400 text-sm font-semibold mb-2">Account Information</h4>
                          <div className="space-y-1 text-xs text-green-500">
                            <div>Email: {user?.email}</div>
                            <div>User ID: {user?.id}</div>
                            <div>Member since: {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</div>
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          <Button
                            className="flex-1 bg-green-600 hover:bg-green-700"
                            onClick={() => {
                              localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings))
                              setIsSettingsOpen(false)
                              addToLog("settings saved")
                            }}
                          >
                            Save Settings
                          </Button>
                          <Button
                            variant="outline"
                            className="flex-1 border-gray-600 text-green-300 hover:bg-gray-700"
                            onClick={() => setIsSettingsOpen(false)}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  <button className="w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded">
                    Import/Export
                  </button>
                </div>
              </div>

              {/* Trash Bin */}
              <div className="mt-6">
                <h3 className="text-green-400 text-sm font-bold mb-2 terminal-glow">&gt; DELETE</h3>
                <div
                  className={`flex items-center justify-center p-4 rounded-lg border-2 border-dashed transition-all duration-300 ${
                    dragOverTrash
                      ? 'border-red-500 bg-red-900/30 scale-110'
                      : 'border-gray-600 hover:border-red-400'
                  }`}
                  onDragOver={(e) => {
                    e.preventDefault()
                    e.dataTransfer.dropEffect = 'move'
                    setDragOverTrash(true)
                    setIsTrashOpen(true)
                  }}
                  onDragLeave={() => {
                    setDragOverTrash(false)
                    setIsTrashOpen(false)
                  }}
                  onDrop={(e) => {
                    e.preventDefault()
                    if (draggedPrompt) {
                      deletePrompt(draggedPrompt.id)
                    }
                    setDragOverTrash(false)
                    setIsTrashOpen(false)
                  }}
                >
                  <div className={`transition-all duration-300 ${
                    isTrashOpen || dragOverTrash ? 'scale-125 text-red-400' : 'text-gray-500'
                  }`}>
                    <Trash2 className={`w-8 h-8 transition-all duration-300 ${
                      isTrashOpen || dragOverTrash ? 'animate-bounce' : ''
                    }`} />
                  </div>
                </div>
                <div className="text-center mt-2">
                  <span className={`text-xs transition-colors duration-300 ${
                    dragOverTrash ? 'text-red-400' : 'text-gray-500'
                  }`}>
                    {dragOverTrash ? 'Release to Delete' : 'Drop to Delete'}
                  </span>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 bg-gray-800 p-6 overflow-y-auto">
              {/* Header with Add Prompt Button */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <h2 className="text-green-400 text-lg font-bold terminal-glow">
                    {selectedFolder === null
                      ? "All Prompts"
                      : folders.find(f => f.id === selectedFolder)?.name || "Folder"}
                  </h2>
                  <span className="text-green-500 text-sm">
                    ({promptsWithFolderInfo.length} prompts)
                  </span>
                  {selectedFolder !== null && (
                    <Button
                      onClick={() => setSelectedFolder(null)}
                      variant="outline"
                      size="sm"
                      className="text-green-400 border-green-600 hover:bg-green-900"
                    >
                      Show All
                    </Button>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={testDatabase}
                    variant="outline"
                    className="text-green-400 border-green-600 hover:bg-green-900"
                  >
                    Test DB
                  </Button>
                  <Dialog open={isNewPromptDialogOpen} onOpenChange={setIsNewPromptDialogOpen}>
                    <DialogTrigger asChild>
                      <Button className="bg-green-600 hover:bg-green-700 text-white">
                        + New Prompt
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-gray-800 border-gray-600 text-green-300 max-w-2xl">
                      <DialogHeader>
                        <DialogTitle className="text-green-400">Create New Prompt</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <Input
                          placeholder="Prompt title"
                          value={newPrompt.title}
                          onChange={(e) => setNewPrompt(prev => ({ ...prev, title: e.target.value }))}
                          className="bg-gray-900 border-gray-600 text-green-300"
                        />
                        <Textarea
                          placeholder="Prompt content"
                          value={newPrompt.content}
                          onChange={(e) => setNewPrompt(prev => ({ ...prev, content: e.target.value }))}
                          className="bg-gray-900 border-gray-600 text-green-300 min-h-32"
                        />
                        <div className="grid grid-cols-2 gap-4">
                          <Select value={newPrompt.category} onValueChange={(value) => setNewPrompt(prev => ({ ...prev, category: value }))}>
                            <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-600">
                              {allCategories.map((category) => (
                                <SelectItem key={category} value={category} className="text-green-300">
                                  {category.charAt(0).toUpperCase() + category.slice(1)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Select value={newPrompt.folder} onValueChange={(value) => setNewPrompt(prev => ({ ...prev, folder: value }))}>
                            <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                              <SelectValue placeholder="Select folder (optional)" />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-600">
                              <SelectItem value="" className="text-green-300">No folder</SelectItem>
                              {folders.map((folder) => (
                                <SelectItem key={folder.id} value={folder.id} className="text-green-300">
                                  {folder.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <Input
                          placeholder="Tags (comma-separated)"
                          value={newPrompt.tags}
                          onChange={(e) => setNewPrompt(prev => ({ ...prev, tags: e.target.value }))}
                          className="bg-gray-900 border-gray-600 text-green-300"
                        />
                        <div className="flex space-x-2">
                          <Button onClick={createPrompt} className="flex-1 bg-green-600 hover:bg-green-700">
                            Create Prompt
                          </Button>
                          <Button variant="outline" onClick={() => setIsNewPromptDialogOpen(false)} className="flex-1">
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              {/* Search and Filter Bar */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4" />
                  <Input
                    placeholder="Search prompts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500"
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48 bg-gray-900 border-gray-600 text-green-300">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                    <SelectItem value="all" className="text-green-300 hover:bg-gray-700">All Categories</SelectItem>
                    <SelectItem value="general" className="text-green-300 hover:bg-gray-700">General</SelectItem>
                    <SelectItem value="development" className="text-green-300 hover:bg-gray-700">Development</SelectItem>
                    <SelectItem value="communication" className="text-green-300 hover:bg-gray-700">Communication</SelectItem>
                    <SelectItem value="analysis" className="text-green-300 hover:bg-gray-700">Analysis</SelectItem>
                    <SelectItem value="creative" className="text-green-300 hover:bg-gray-700">Creative</SelectItem>
                    {userSettings.customCategories.map((category) => (
                      <SelectItem key={category} value={category} className="text-green-300 hover:bg-gray-700">
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40 bg-gray-900 border-gray-600 text-green-300">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                    <SelectItem value="smart" className="text-green-300 hover:bg-gray-700">Smart Sort</SelectItem>
                    <SelectItem value="rating" className="text-green-300 hover:bg-gray-700">By Rating</SelectItem>
                    <SelectItem value="date" className="text-green-300 hover:bg-gray-700">By Date</SelectItem>
                    <SelectItem value="title" className="text-green-300 hover:bg-gray-700">By Title</SelectItem>
                  </SelectContent>
                </Select>

                {/* Layout Selector */}
                <div className="flex items-center space-x-2">
                  <span className="text-green-400 text-sm">View:</span>
                  <div className="flex items-center space-x-1 bg-gray-900 rounded-lg p-1">
                    <button
                      onClick={() => setLayoutMode('large')}
                      className={`p-2 rounded transition-colors ${
                        layoutMode === 'large' ? 'bg-green-600 text-white' : 'text-green-400 hover:bg-gray-700'
                      }`}
                      title="Large cards (3 per row)"
                    >
                      <Grid2X2 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setLayoutMode('medium')}
                      className={`p-2 rounded transition-colors ${
                        layoutMode === 'medium' ? 'bg-green-600 text-white' : 'text-green-400 hover:bg-gray-700'
                      }`}
                      title="Medium cards (4 per row)"
                    >
                      <LayoutGrid className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setLayoutMode('small')}
                      className={`p-2 rounded transition-colors ${
                        layoutMode === 'small' ? 'bg-green-600 text-white' : 'text-green-400 hover:bg-gray-700'
                      }`}
                      title="Small cards (5 per row)"
                    >
                      <Grid3X3 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setLayoutMode('list')}
                      className={`p-2 rounded transition-colors ${
                        layoutMode === 'list' ? 'bg-green-600 text-white' : 'text-green-400 hover:bg-gray-700'
                      }`}
                      title="List view (compact)"
                    >
                      <List className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Folder Icons */}
              {folders.length > 0 && (
                <div className="mb-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <h3 className="text-green-400 text-sm font-semibold">Quick Access Folders:</h3>
                  </div>
                  <div className="flex flex-wrap gap-3">
                    {/* All Folders Icon */}
                    <div
                      className={`flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 ${
                        dragOverFolder === 'all' ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50'
                      } ${selectedFolder === null ? 'bg-green-900/30 border-green-500' : ''}`}
                      onClick={() => setSelectedFolder(null)}
                      onDragOver={(e) => {
                        e.preventDefault()
                        e.dataTransfer.dropEffect = 'move'
                        setDragOverFolder('all')
                      }}
                      onDragLeave={() => setDragOverFolder(null)}
                      onDrop={(e) => {
                        e.preventDefault()
                        if (draggedPrompt) {
                          movePromptToFolder(draggedPrompt.id, null)
                        }
                        setDragOverFolder(null)
                      }}
                    >
                      <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-2">
                        <Folder className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-xs text-green-300 text-center font-medium">All</span>
                      <span className="text-xs text-green-500">({prompts.length})</span>
                    </div>

                    {/* Individual Folder Icons */}
                    {folders.map((folder) => (
                      <div
                        key={folder.id}
                        className={`flex flex-col items-center p-3 rounded-lg border-2 border-dashed border-gray-600 hover:border-green-500 cursor-pointer transition-all hover:scale-105 ${
                          dragOverFolder === folder.id ? 'border-green-400 bg-green-900/20 scale-105' : 'hover:bg-gray-800/50'
                        } ${selectedFolder === folder.id ? 'bg-green-900/30 border-green-500' : ''}`}
                        onClick={() => setSelectedFolder(selectedFolder === folder.id ? null : folder.id)}
                        onDragOver={(e) => {
                          e.preventDefault()
                          e.dataTransfer.dropEffect = 'move'
                          setDragOverFolder(folder.id)
                        }}
                        onDragLeave={() => setDragOverFolder(null)}
                        onDrop={(e) => {
                          e.preventDefault()
                          if (draggedPrompt) {
                            movePromptToFolder(draggedPrompt.id, folder.id)
                          }
                          setDragOverFolder(null)
                        }}
                      >
                        <div className={`w-12 h-12 ${folder.color} rounded-lg flex items-center justify-center mb-2`}>
                          <Folder className="w-6 h-6 text-white" />
                        </div>
                        <span className="text-xs text-green-300 text-center font-medium max-w-16 truncate">
                          {folder.name}
                        </span>
                        <span className="text-xs text-green-500">({folder.promptCount || 0})</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Prompts Grid */}
              <div className={`grid gap-4 ${
                layoutMode === 'large' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                layoutMode === 'medium' ? 'grid-cols-1 md:grid-cols-3 lg:grid-cols-4' :
                layoutMode === 'small' ? 'grid-cols-1 md:grid-cols-4 lg:grid-cols-5' :
                'grid-cols-1'
              }`}>
                {loadingPrompts ? (
                  <div className="col-span-full text-center text-green-400 py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4"></div>
                    Loading prompts...
                  </div>
                ) : promptsWithFolderInfo.length === 0 ? (
                  <div className="col-span-full text-center text-green-400 py-8">
                    {selectedFolder !== null
                      ? `No prompts in "${folders.find(f => f.id === selectedFolder)?.name}" folder yet.`
                      : searchTerm || selectedCategory !== "all"
                        ? "No prompts match your search."
                        : "No prompts yet. Create your first prompt!"
                    }
                    <div className="mt-4">
                      <Button
                        onClick={() => setIsNewPromptDialogOpen(true)}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        + Create First Prompt
                      </Button>
                    </div>
                  </div>
                ) : (
                  promptsWithFolderInfo.map((prompt) => (
                    layoutMode === 'list' ? (
                      // List View
                      <div
                        key={prompt.id}
                        draggable
                        onDragStart={(e) => {
                          setDraggedPrompt(prompt)
                          e.dataTransfer.effectAllowed = 'move'
                          e.dataTransfer.setData('text/plain', prompt.id)
                          e.currentTarget.style.opacity = '0.5'
                        }}
                        onDragEnd={(e) => {
                          setDraggedPrompt(null)
                          setDragOverFolder(null)
                          setDragOverTrash(false)
                          setIsTrashOpen(false)
                          e.currentTarget.style.opacity = '1'
                        }}
                        className={`flex items-center justify-between p-3 bg-gray-900 border border-gray-700 hover:border-green-500 rounded-lg transition-colors cursor-move ${
                          prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3 flex-1">
                          <div className="text-green-400 text-xs opacity-30">⋮⋮</div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h3 className="text-green-400 text-sm font-medium">{prompt.title}</h3>
                              {prompt.is_pinned && <Pin className="w-3 h-3 text-green-500" />}
                            </div>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline" className="text-xs text-blue-400 border-blue-500 bg-blue-900/20">
                                {prompt.category.toUpperCase()}
                              </Badge>
                              {(prompt as any).folderInfo && (
                                <Badge variant="outline" className="text-xs text-purple-400 border-purple-500 bg-purple-900/20">
                                  {(prompt as any).folderInfo.name}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-3 h-3 cursor-pointer transition-colors hover:text-green-300 ${
                                  i < prompt.rating ? "text-green-400 fill-current" : "text-gray-600 hover:text-gray-400"
                                }`}
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const newRating = i + 1
                                  if (newRating === prompt.rating) {
                                    updateRating(prompt.id, 0)
                                  } else {
                                    updateRating(prompt.id, newRating)
                                  }
                                }}
                              />
                            ))}
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => copyPrompt(prompt)}
                              className="text-green-400 border-green-600 hover:bg-green-900 h-6 w-6 p-0"
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openEditDialog(prompt)}
                              className="text-green-400 border-green-600 hover:bg-green-900 h-6 w-6 p-0"
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      // Card Views (Large, Medium, Small)
                      <Card
                        key={prompt.id}
                        draggable
                        onDragStart={(e) => {
                          setDraggedPrompt(prompt)
                          e.dataTransfer.effectAllowed = 'move'
                          e.dataTransfer.setData('text/plain', prompt.id)
                          e.currentTarget.style.opacity = '0.5'
                        }}
                        onDragEnd={(e) => {
                          setDraggedPrompt(null)
                          setDragOverFolder(null)
                          setDragOverTrash(false)
                          setIsTrashOpen(false)
                          e.currentTarget.style.opacity = '1'
                        }}
                        className={`bg-gray-900 border-gray-700 hover:border-green-500 transition-colors cursor-move ${
                          prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''
                        }`}
                      >
                        <CardHeader className={layoutMode === 'small' ? 'pb-1' : 'pb-2'}>
                          <div className="flex items-start justify-between">
                            <div className="absolute top-2 right-2 opacity-30 hover:opacity-60 transition-opacity">
                              <div className="text-green-400 text-xs">⋮⋮</div>
                            </div>
                            <CardTitle className={`text-green-400 font-bold ${
                              layoutMode === 'small' ? 'text-xs' : 'text-sm'
                            }`}>{prompt.title}</CardTitle>
                            <div className="flex items-center space-x-1">
                              {prompt.is_pinned && <Pin className="w-3 h-3 text-green-500" />}
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`w-3 h-3 cursor-pointer transition-colors hover:text-green-300 ${
                                      i < prompt.rating ? "text-green-400 fill-current" : "text-gray-600 hover:text-gray-400"
                                    }`}
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      const newRating = i + 1
                                      if (newRating === prompt.rating) {
                                        updateRating(prompt.id, 0)
                                      } else {
                                        updateRating(prompt.id, newRating)
                                      }
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                        </CardHeader>

                        {layoutMode !== 'small' && (
                          <CardContent className="pt-0">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline" className="text-xs text-blue-400 border-blue-500 bg-blue-900/20">
                                  {prompt.category.toUpperCase()}
                                </Badge>
                                {(prompt as any).folderInfo && (
                                  <Badge variant="outline" className="text-xs text-purple-400 border-purple-500 bg-purple-900/20">
                                    {(prompt as any).folderInfo.name}
                                  </Badge>
                                )}
                              </div>

                              {layoutMode === 'large' && (
                                <p className="text-green-300 text-xs line-clamp-3">
                                  {prompt.content}
                                </p>
                              )}

                              {prompt.tags.length > 0 && layoutMode === 'large' && (
                                <div className="flex flex-wrap gap-1">
                                  {prompt.tags.slice(0, 3).map((tag, index) => (
                                    <span key={index} className="text-xs bg-gray-800 text-green-500 px-2 py-1 rounded">
                                      #{tag}
                                    </span>
                                  ))}
                                  {prompt.tags.length > 3 && (
                                    <span className="text-xs text-gray-500">+{prompt.tags.length - 3} more</span>
                                  )}
                                </div>
                              )}

                              <div className="flex items-center justify-between pt-2">
                                <div className="flex items-center space-x-1">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => copyPrompt(prompt)}
                                    className="text-green-400 border-green-600 hover:bg-green-900 h-6 w-6 p-0"
                                  >
                                    <Copy className="w-3 h-3" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => openEditDialog(prompt)}
                                    className="text-green-400 border-green-600 hover:bg-green-900 h-6 w-6 p-0"
                                  >
                                    <Edit className="w-3 h-3" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => togglePin(prompt.id)}
                                    className="text-green-400 border-green-600 hover:bg-green-900 h-6 w-6 p-0"
                                  >
                                    <Pin className="w-3 h-3" />
                                  </Button>
                                </div>

                                {layoutMode === 'large' && (
                                  <div className="text-xs text-gray-500">
                                    {new Date(prompt.created_at).toLocaleDateString()}
                                  </div>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        )}
                      </Card>
                    )
                  ))
                )}
              </div>

              {/* Edit Prompt Dialog */}
              <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="bg-gray-800 border-gray-600 text-green-300 max-w-2xl">
                  <DialogHeader>
                    <DialogTitle className="text-green-400">Edit Prompt</DialogTitle>
                  </DialogHeader>
                  {editingPrompt && (
                    <div className="space-y-4">
                      <Input
                        placeholder="Prompt title"
                        value={editingPrompt.title}
                        onChange={(e) => setEditingPrompt(prev => prev ? { ...prev, title: e.target.value } : null)}
                        className="bg-gray-900 border-gray-600 text-green-300"
                      />
                      <Textarea
                        placeholder="Prompt content"
                        value={editingPrompt.content}
                        onChange={(e) => setEditingPrompt(prev => prev ? { ...prev, content: e.target.value } : null)}
                        className="bg-gray-900 border-gray-600 text-green-300 min-h-32"
                      />
                      <div className="grid grid-cols-2 gap-4">
                        <Select
                          value={editingPrompt.category}
                          onValueChange={(value) => setEditingPrompt(prev => prev ? { ...prev, category: value } : null)}
                        >
                          <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-800 border-gray-600">
                            {allCategories.map((category) => (
                              <SelectItem key={category} value={category} className="text-green-300">
                                {category.charAt(0).toUpperCase() + category.slice(1)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Input
                          placeholder="Tags (comma-separated)"
                          value={editingPrompt.tags.join(', ')}
                          onChange={(e) => setEditingPrompt(prev => prev ? {
                            ...prev,
                            tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                          } : null)}
                          className="bg-gray-900 border-gray-600 text-green-300"
                        />
                      </div>
                      <div className="flex space-x-2">
                        <Button onClick={updatePrompt} className="flex-1 bg-green-600 hover:bg-green-700">
                          Update Prompt
                        </Button>
                        <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} className="flex-1">
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Status Bar */}
          <div className="bg-gray-700 px-6 py-2 border-t border-gray-600">
            <div className="flex items-center justify-between text-xs text-green-500">
              <div className="flex items-center space-x-4">
                {loadingPrompts ? (
                  <>
                    <span>Loading...</span>
                    <span>|</span>
                    <span>Please wait</span>
                  </>
                ) : (
                  <>
                    <span>Ready</span>
                    <span>|</span>
                    <span>{promptsWithFolderInfo.length} prompts</span>
                    <span>|</span>
                    <span>{folders.length} folders</span>
                  </>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-3 h-3" />
                <span>Last activity: {activityLog[activityLog.length - 1]}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
