"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Search, Star, Copy, Edit, Pin, Clock, Terminal, LogOut, Folder, Trash2, Grid3X3, List, LayoutGrid, Menu, ChevronLeft } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { promptService, folderService, promptFolderService, subscriptions } from "@/lib/database"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Prompt {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  rating: number
  is_pinned: boolean
  created_at: string
  updated_at: string
  last_used: string | null
  user_id: string
}

interface FolderType {
  id: string
  name: string
  color: string
  created_at: string
  updated_at: string
  user_id: string
  promptCount?: number
}

export default function TerminalPromptManager() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()

  const [currentTime, setCurrentTime] = useState(new Date())
  const [currentPath, setCurrentPath] = useState("/prompts")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [sortBy, setSortBy] = useState("smart") // smart, date, title, rating
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [folders, setFolders] = useState<FolderType[]>([])
  const [loadingPrompts, setLoadingPrompts] = useState(true)
  const [loadingFolders, setLoadingFolders] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [newPrompt, setNewPrompt] = useState({
    title: "",
    content: "",
    category: "general",
    tags: [] as string[],
    folderId: "none"
  })
  const [isCreatingPrompt, setIsCreatingPrompt] = useState(false)
  const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = useState(false)
  const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = useState(false)
  const [newFolderName, setNewFolderName] = useState("")
  const [isCreatingFolder, setIsCreatingFolder] = useState(false)
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [userSettings, setUserSettings] = useState({
    displayName: "",
    theme: "dark",
    defaultCategory: "general",
    autoSave: true,
    customCategories: [] as string[]
  })
  const [newCustomCategory, setNewCustomCategory] = useState("")
  const [isAddingCustomCategory, setIsAddingCustomCategory] = useState(false)
  const [customCategoryInput, setCustomCategoryInput] = useState("")
  const [draggedPrompt, setDraggedPrompt] = useState<Prompt | null>(null)
  const [dragOverFolder, setDragOverFolder] = useState<string | null>(null)
  const [isTrashOpen, setIsTrashOpen] = useState(false)
  const [dragOverTrash, setDragOverTrash] = useState(false)
  const [viewMode, setViewMode] = useState<"grid" | "compact" | "list">("grid")
  const [gridSize, setGridSize] = useState<"small" | "medium" | "large">("medium")
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const [activityLog, setActivityLog] = useState([
    "user@promptmanager: initializing system...",
  ])

  // Load prompts from database with folder relationships
  const loadPrompts = async () => {
    if (!user) return

    try {
      setLoadingPrompts(true)
      const data = await promptService.getAll(user.id)

      // Load folder relationships for each prompt
      const promptsWithFolders = await Promise.all(
        data.map(async (prompt) => {
          try {
            const folder = await promptFolderService.getFolderForPrompt(prompt.id)
            if (folder) {
              (prompt as any).folderInfo = {
                id: folder.id,
                name: folder.name,
                color: folder.color
              }
            }
            return prompt
          } catch (err) {
            console.error(`Failed to load folder for prompt ${prompt.id}:`, err)
            return prompt
          }
        })
      )

      setPrompts(promptsWithFolders)
      addToLog(`loaded ${data.length} prompts with folder relationships`)
    } catch (err: any) {
      setError(err.message)
      addToLog(`error loading prompts: ${err.message}`)
    } finally {
      setLoadingPrompts(false)
    }
  }

  // Load folders from database
  const loadFolders = async () => {
    if (!user) return

    try {
      setLoadingFolders(true)
      const data = await folderService.getAll(user.id)
      setFolders(data)
      addToLog(`loaded ${data.length} folders`)
    } catch (err: any) {
      setError(err.message)
      addToLog(`error loading folders: ${err.message}`)
    } finally {
      setLoadingFolders(false)
    }
  }

  // Update folder prompt counts
  const updateFolderCounts = () => {
    setFolders(prevFolders =>
      prevFolders.map(folder => ({
        ...folder,
        promptCount: prompts.filter(prompt =>
          (prompt as any).folderInfo?.id === folder.id
        ).length
      }))
    )
  }

  // Authentication check
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }
  }, [user, loading, router])

  // Load data when user is authenticated
  useEffect(() => {
    if (user) {
      loadPrompts()
      loadFolders()

      // Load saved settings from localStorage
      const savedSettings = localStorage.getItem('promptManagerSettings')
      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings)
          setUserSettings(prev => ({ ...prev, ...parsed }))
        } catch (err) {
          console.error('Failed to load saved settings:', err)
        }
      }

      // Initialize user settings
      setUserSettings(prev => ({
        ...prev,
        displayName: prev.displayName || user.email?.split('@')[0] || "User"
      }))
      addToLog("system ready")
    }
  }, [user])

  // Update folder counts when prompts change
  useEffect(() => {
    updateFolderCounts()
  }, [prompts])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user) return

    // Subscribe to prompt changes
    const promptSubscription = subscriptions.subscribeToPrompts(user.id, (payload) => {
      addToLog(`real-time update: ${payload.eventType} prompt`)

      if (payload.eventType === 'INSERT') {
        setPrompts(prev => [payload.new, ...prev])
      } else if (payload.eventType === 'UPDATE') {
        setPrompts(prev => prev.map(p => p.id === payload.new.id ? payload.new : p))
      } else if (payload.eventType === 'DELETE') {
        setPrompts(prev => prev.filter(p => p.id !== payload.old.id))
      }
    })

    // Subscribe to folder changes
    const folderSubscription = subscriptions.subscribeToFolders(user.id, (payload) => {
      addToLog(`real-time update: ${payload.eventType} folder`)

      if (payload.eventType === 'INSERT') {
        setFolders(prev => [payload.new, ...prev])
      } else if (payload.eventType === 'UPDATE') {
        setFolders(prev => prev.map(f => f.id === payload.new.id ? payload.new : f))
      } else if (payload.eventType === 'DELETE') {
        setFolders(prev => prev.filter(f => f.id !== payload.old.id))
      }
    })

    // Cleanup subscriptions
    return () => {
      promptSubscription.unsubscribe()
      folderSubscription.unsubscribe()
    }
  }, [user])

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 p-8 text-center">
          <div className="text-green-400 text-lg mb-4">Loading...</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"></div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!user) {
    return null
  }

  // Get all available categories (default + custom)
  const allCategories = [
    "general", "development", "communication", "analysis", "creative",
    ...userSettings.customCategories
  ]

  // Advanced sorting function
  const sortPrompts = (prompts: Prompt[]) => {
    return prompts.sort((a, b) => {
      // 1. Always prioritize pinned prompts
      if (a.is_pinned && !b.is_pinned) return -1
      if (!a.is_pinned && b.is_pinned) return 1

      // 2. Apply secondary sorting
      switch (sortBy) {
        case "smart":
          // Smart: Pin > Rating > Recent
          if (a.rating !== b.rating) return b.rating - a.rating
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()

        case "rating":
          // Rating: Pin > Rating > Title
          if (a.rating !== b.rating) return b.rating - a.rating
          return a.title.localeCompare(b.title)

        case "date":
          // Date: Pin > Recent > Old
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()

        case "title":
          // Title: Pin > Alphabetical
          return a.title.localeCompare(b.title)

        default:
          return 0
      }
    })
  }

  const filteredPrompts = sortPrompts(
    prompts.filter((prompt) => {
      const matchesSearch =
        prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prompt.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      const matchesCategory = selectedCategory === "all" || prompt.category === selectedCategory

      // Folder filtering
      const matchesFolder = selectedFolder === null || (prompt as any).folderInfo?.id === selectedFolder

      return matchesSearch && matchesCategory && matchesFolder
    })
  )

  const addToLog = (message: string) => {
    setActivityLog((prev) => [...prev.slice(-4), `user@promptmanager: ${message}`])
  }

  const copyPrompt = async (prompt: Prompt) => {
    try {
      await navigator.clipboard.writeText(prompt.content)
      // Update last used timestamp
      if (user) {
        await promptService.updateLastUsed(prompt.id, user.id)
        loadPrompts() // Refresh the list
      }
      addToLog(`copied "${prompt.title}"`)
    } catch (err) {
      addToLog(`failed to copy "${prompt.title}"`)
    }
  }

  const togglePin = async (id: string) => {
    if (!user) return

    try {
      const prompt = prompts.find(p => p.id === id)
      const updatedPrompt = await promptService.togglePin(id, user.id)

      // Preserve folder info when updating
      if (prompt && (prompt as any).folderInfo) {
        (updatedPrompt as any).folderInfo = (prompt as any).folderInfo
      }

      setPrompts((prev) => prev.map((p) => (p.id === id ? updatedPrompt : p)))
      addToLog(`${updatedPrompt.is_pinned ? "pinned" : "unpinned"} "${updatedPrompt.title}"`)
    } catch (err: any) {
      addToLog(`failed to toggle pin: ${err.message}`)
    }
  }

  const updateRating = async (promptId: string, rating: number) => {
    if (!user) return

    try {
      const prompt = prompts.find(p => p.id === promptId)
      if (!prompt) return

      const updatedPrompt = await promptService.update(
        promptId,
        { rating },
        user.id
      )

      // Preserve folder info when updating
      if ((prompt as any).folderInfo) {
        (updatedPrompt as any).folderInfo = (prompt as any).folderInfo
      }

      setPrompts(prev => prev.map(p => p.id === promptId ? updatedPrompt : p))
      addToLog(`rated prompt "${updatedPrompt.title}" ${rating} stars`)
    } catch (err: any) {
      addToLog(`failed to update rating: ${err.message}`)
    }
  }

  const addCustomCategory = (categoryName: string) => {
    const category = categoryName.trim().toLowerCase()
    if (category && !allCategories.includes(category)) {
      // Add to user settings
      const updatedSettings = {
        ...userSettings,
        customCategories: [...userSettings.customCategories, category]
      }
      setUserSettings(updatedSettings)

      // Save to localStorage immediately
      localStorage.setItem('promptManagerSettings', JSON.stringify(updatedSettings))

      addToLog(`added custom category "${category}"`)
      return category
    }
    return null
  }

  const movePromptToFolder = async (promptId: string, targetFolderId: string | null) => {
    if (!user) return

    try {
      const prompt = prompts.find(p => p.id === promptId)
      if (!prompt) return

      const currentFolderId = (prompt as any).folderInfo?.id || null

      // Don't do anything if dropping in the same folder
      if (currentFolderId === targetFolderId) return

      // Update database relationship
      await promptFolderService.updatePromptFolder(
        promptId,
        currentFolderId,
        targetFolderId
      )

      // Update local state
      setPrompts(prev => prev.map(p => {
        if (p.id === promptId) {
          const updatedPrompt = { ...p }
          if (targetFolderId) {
            const targetFolder = folders.find(f => f.id === targetFolderId)
            if (targetFolder) {
              (updatedPrompt as any).folderInfo = {
                id: targetFolder.id,
                name: targetFolder.name,
                color: targetFolder.color
              }
            }
          } else {
            delete (updatedPrompt as any).folderInfo
          }
          return updatedPrompt
        }
        return p
      }))

      const targetFolderName = targetFolderId
        ? folders.find(f => f.id === targetFolderId)?.name || "folder"
        : "no folder"

      addToLog(`moved "${prompt.title}" to ${targetFolderName}`)
    } catch (err: any) {
      addToLog(`failed to move prompt: ${err.message}`)
    }
  }

  const deletePrompt = async (promptId: string) => {
    if (!user) return

    try {
      const prompt = prompts.find(p => p.id === promptId)
      if (!prompt) return

      // Delete from database
      await promptService.delete(promptId, user.id)

      // Remove from local state
      setPrompts(prev => prev.filter(p => p.id !== promptId))

      addToLog(`deleted prompt "${prompt.title}"`)
    } catch (err: any) {
      addToLog(`failed to delete prompt: ${err.message}`)
    }
  }

  const handleSignOut = async () => {
    try {
      await signOut()
      addToLog("signed out")
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  // Test database connectivity
  const testDatabase = async () => {
    if (!user) return

    try {
      console.log('Testing database connectivity...')

      // Test prompts table
      const prompts = await promptService.getAll(user.id)
      console.log('Prompts loaded:', prompts.length)

      // Test folders table
      const folders = await folderService.getAll(user.id)
      console.log('Folders loaded:', folders.length)

      // Test prompt_folders table if we have prompts
      if (prompts.length > 0) {
        const folder = await promptFolderService.getFolderForPrompt(prompts[0].id)
        console.log('Folder relationship test:', folder)
      }

      addToLog("database test completed - check console")
    } catch (err: any) {
      console.error('Database test failed:', err)
      addToLog(`database test failed: ${err.message}`)
    }
  }

  const createPrompt = async () => {
    if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return

    try {
      setIsCreatingPrompt(true)
      // Add default tags based on category if no tags provided
      let tags = newPrompt.tags
      if (tags.length === 0) {
        const defaultTags: Record<string, string[]> = {
          development: ['code', 'programming'],
          communication: ['email', 'writing'],
          analysis: ['data', 'insights'],
          creative: ['brainstorm', 'ideas'],
          general: ['prompt']
        }
        tags = defaultTags[newPrompt.category] || ['prompt']
      }

      const prompt = await promptService.create({
        title: newPrompt.title.trim(),
        content: newPrompt.content.trim(),
        category: newPrompt.category,
        tags: tags,
        user_id: user.id
      })

      // Add to folder if selected
      if (newPrompt.folderId !== "none") {
        try {
          console.log('Adding prompt to folder:', { promptId: prompt.id, folderId: newPrompt.folderId })
          await promptFolderService.addPromptToFolder(prompt.id, newPrompt.folderId)

          const selectedFolder = folders.find(f => f.id === newPrompt.folderId)
          if (selectedFolder) {
            // Store folder info in the prompt object for display
            (prompt as any).folderInfo = {
              id: selectedFolder.id,
              name: selectedFolder.name,
              color: selectedFolder.color
            }
            console.log('Added folder info to new prompt:', selectedFolder.name)
          }
        } catch (err) {
          console.error('Failed to add prompt to folder:', err)
          addToLog(`failed to add to folder: ${(err as Error).message}`)
        }
      }

      setPrompts(prev => [prompt, ...prev])
      setNewPrompt({ title: "", content: "", category: userSettings.defaultCategory, tags: [], folderId: "none" })
      setIsAddingCustomCategory(false)
      setCustomCategoryInput("")
      setIsNewPromptDialogOpen(false) // Close the dialog
      addToLog(`created prompt "${prompt.title}"`)
    } catch (err: any) {
      addToLog(`failed to create prompt: ${err.message}`)
    } finally {
      setIsCreatingPrompt(false)
    }
  }

  const createFolder = async () => {
    if (!user || !newFolderName.trim()) return

    try {
      setIsCreatingFolder(true)
      const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500', 'bg-yellow-500']
      const randomColor = colors[Math.floor(Math.random() * colors.length)]

      const folder = await folderService.create({
        name: newFolderName.trim(),
        color: randomColor,
        user_id: user.id
      })

      setFolders(prev => [folder, ...prev])
      setNewFolderName("")
      setIsNewFolderDialogOpen(false)
      addToLog(`created folder "${folder.name}"`)
    } catch (err: any) {
      addToLog(`failed to create folder: ${err.message}`)
    } finally {
      setIsCreatingFolder(false)
    }
  }

  const openEditDialog = async (prompt: Prompt) => {
    try {
      // Load current folder relationship from database
      const folder = await promptFolderService.getFolderForPrompt(prompt.id)
      const promptWithFolder = {
        ...prompt,
        currentFolderId: folder?.id || "none",
        folderInfo: folder ? {
          id: folder.id,
          name: folder.name,
          color: folder.color
        } : undefined
      }
      setEditingPrompt(promptWithFolder as any)
      setIsEditDialogOpen(true)
    } catch (err) {
      console.error('Failed to load folder for editing:', err)
      // Fallback to current folder info
      const promptWithFolder = {
        ...prompt,
        currentFolderId: (prompt as any).folderInfo?.id || "none"
      }
      setEditingPrompt(promptWithFolder as any)
      setIsEditDialogOpen(true)
    }
  }

  const updatePrompt = async () => {
    if (!user || !editingPrompt) return

    try {
      const updatedPrompt = await promptService.update(
        editingPrompt.id,
        {
          title: editingPrompt.title,
          content: editingPrompt.content,
          category: editingPrompt.category,
          tags: editingPrompt.tags
        },
        user.id
      )

      // Update folder relationship if changed
      const newFolderId = (editingPrompt as any).currentFolderId
      const oldFolderId = (editingPrompt as any).folderInfo?.id || null

      console.log('Folder update check:', { newFolderId, oldFolderId, comparison: newFolderId !== (oldFolderId || "none") })

      if (newFolderId !== (oldFolderId || "none")) {
        try {
          console.log('Updating folder relationship:', { promptId: updatedPrompt.id, oldFolderId, newFolderId })

          await promptFolderService.updatePromptFolder(
            updatedPrompt.id,
            oldFolderId,
            newFolderId !== "none" ? newFolderId : null
          )

          console.log('Folder relationship updated successfully')

          // Update folder info for display
          if (newFolderId !== "none") {
            const selectedFolder = folders.find(f => f.id === newFolderId)
            if (selectedFolder) {
              (updatedPrompt as any).folderInfo = {
                id: selectedFolder.id,
                name: selectedFolder.name,
                color: selectedFolder.color
              }
              console.log('Added folder info to prompt:', selectedFolder.name)
            }
          } else {
            delete (updatedPrompt as any).folderInfo
            console.log('Removed folder info from prompt')
          }
        } catch (err) {
          console.error('Failed to update folder relationship:', err)
          addToLog(`failed to update folder: ${(err as Error).message}`)
        }
      } else {
        console.log('No folder change needed')
      }

      setPrompts(prev => prev.map(p => p.id === updatedPrompt.id ? updatedPrompt : p))
      setIsEditDialogOpen(false)
      setEditingPrompt(null)
      addToLog(`updated prompt "${updatedPrompt.title}"`)
    } catch (err: any) {
      addToLog(`failed to update prompt: ${err.message}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 p-0">
      <div className="w-full h-screen">
        {/* Terminal Frame */}
        <div className="bg-gray-800 h-screen overflow-hidden">
          {/* Top Navigation Bar */}
          <div className="bg-gray-700 px-3 sm:px-6 py-3 border-b border-gray-600">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div className="flex items-center space-x-3 sm:space-x-6 mb-2 sm:mb-0">
                <div
                  className="flex items-center space-x-2 sm:space-x-3 cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={() => {
                    setSelectedFolder(null)
                    setSearchTerm("")
                    setSelectedCategory("all")
                    setSortBy("smart")
                  }}
                >
                  <div className="text-green-400 font-black text-xl sm:text-2xl terminal-glow select-none">
                    &gt;<span className="animate-pulse">_</span>
                  </div>
                  <div className="text-green-400 font-bold text-sm sm:text-lg terminal-glow">PROMPT MANAGER</div>
                </div>
                <nav className="hidden sm:flex items-center space-x-4 bg-gray-600 rounded-full px-4 py-1">
                  <button className="text-green-400 hover:text-green-300 text-sm">Home</button>
                  <button className="text-green-400 hover:text-green-300 text-sm">Folders</button>
                  <button
                    onClick={() => setIsSettingsOpen(true)}
                    className="text-green-400 hover:text-green-300 text-sm"
                  >
                    Settings
                  </button>
                  <button className="text-green-400 hover:text-green-300 text-sm">Help</button>
                </nav>
              </div>
              <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                <div className="text-green-400 text-xs sm:text-sm">{user?.email}</div>
                <button
                  onClick={handleSignOut}
                  className="text-green-400 hover:text-green-300 text-xs sm:text-sm flex items-center space-x-1"
                >
                  <LogOut className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>Logout</span>
                </button>
                <div className="hidden sm:block text-green-400 text-sm">{currentTime.toLocaleTimeString()}</div>
              </div>
            </div>
          </div>

          {/* Path Display */}
          <div className="bg-gray-900 px-3 sm:px-6 py-2 border-b border-gray-700">
            <div className="flex items-center justify-between text-green-400 text-xs sm:text-sm">
              <div className="flex items-center space-x-2">
                <Terminal className="w-3 h-3 sm:w-4 sm:h-4" />
                <span>user@promptmanager:</span>
                <span className="text-green-300">{currentPath}</span>
                <span className="animate-pulse">_</span>
              </div>
              <div className="flex items-center space-x-2 sm:space-x-4">
                <span>Online</span>
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row h-[calc(100vh-120px)]">
            {/* Sidebar */}
            <div className={`${sidebarCollapsed ? 'hidden lg:block lg:w-12' : 'w-full lg:w-64'} bg-gray-900 border-b lg:border-b-0 lg:border-r border-gray-700 lg:h-full overflow-y-auto transition-all duration-300 ease-in-out rounded-lg lg:rounded-none lg:rounded-r-xl shadow-lg`}>
              {/* Sidebar Toggle Button */}
              <div className="hidden lg:flex justify-end p-2">
                <Button
                  onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                  variant="ghost"
                  size="sm"
                  className="text-green-400 hover:text-green-300 hover:bg-gray-800 rounded-full h-8 w-8 p-0"
                >
                  {sidebarCollapsed ? <Menu className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
                </Button>
              </div>
              
              {!sidebarCollapsed && (
                <div className="p-2 sm:p-4 space-y-4">
                  {/* Folders Section */}
                  <div>
                    <h3 className="text-green-400 text-xs sm:text-sm font-bold mb-2 terminal-glow">&gt; FOLDERS</h3>
                    <div className="space-y-1">
                      <div
                        className={`flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-800 cursor-pointer transition-colors ${
                          selectedFolder === null ? 'bg-gray-700 border-l-2 border-green-400' : ''
                        } ${dragOverFolder === 'all' ? 'bg-green-800/30 border-green-400' : ''}`}
                        onClick={() => setSelectedFolder(null)}
                        onDragOver={(e) => {
                          e.preventDefault()
                          e.dataTransfer.dropEffect = 'move'
                          setDragOverFolder('all')
                        }}
                        onDragLeave={() => setDragOverFolder(null)}
                        onDrop={(e) => {
                          e.preventDefault()
                          if (draggedPrompt) {
                            movePromptToFolder(draggedPrompt.id, null)
                          }
                          setDragOverFolder(null)
                        }}
                      >
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span className="text-green-300 text-sm flex-1">All Folders</span>
                        <span className="text-green-500 text-xs">{prompts.length}</span>
                      </div>
                      {folders.map((folder) => (
                        <div
                          key={folder.id}
                          className={`flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-800 cursor-pointer transition-colors ${
                            selectedFolder === folder.id ? 'bg-gray-700 border-l-2 border-green-400' : ''
                          } ${dragOverFolder === folder.id ? 'bg-green-800/30 border-green-400 border' : ''}`}
                          onClick={() => setSelectedFolder(selectedFolder === folder.id ? null : folder.id)}
                          onDragOver={(e) => {
                            e.preventDefault()
                            e.dataTransfer.dropEffect = 'move'
                            setDragOverFolder(folder.id)
                          }}
                          onDragLeave={() => setDragOverFolder(null)}
                          onDrop={(e) => {
                            e.preventDefault()
                            if (draggedPrompt) {
                              movePromptToFolder(draggedPrompt.id, folder.id)
                            }
                            setDragOverFolder(null)
                          }}
                        >
                          <div className={`w-3 h-3 rounded-full ${folder.color}`}></div>
                          <span className="text-green-300 text-sm flex-1">{folder.name}</span>
                          <span className="text-green-500 text-xs">{folder.promptCount || 0}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                {/* Quick Actions */}
                <div>
                  <h3 className="text-green-400 text-sm font-bold mb-2 terminal-glow">&gt; ACTIONS</h3>
                  <div className="space-y-1">
                    <Dialog
                      open={isNewPromptDialogOpen}
                      onOpenChange={(open) => {
                        setIsNewPromptDialogOpen(open)
                        if (!open) {
                          setIsAddingCustomCategory(false)
                          setCustomCategoryInput("")
                        }
                      }}
                    >
                      <DialogTrigger asChild>
                        <button className="w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded">
                          + New Prompt
                        </button>
                      </DialogTrigger>
                      <DialogContent className="bg-gray-800 border-gray-600 text-green-300 rounded-xl">
                        <DialogHeader>
                          <DialogTitle className="text-green-400">Create New Prompt</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <Input
                            placeholder="Prompt title..."
                            className="bg-gray-900 border-gray-600 text-green-300"
                            value={newPrompt.title}
                            onChange={(e) => setNewPrompt(prev => ({ ...prev, title: e.target.value }))}
                          />
                          <Textarea
                            placeholder="Prompt content..."
                            className="bg-gray-900 border-gray-600 text-green-300 h-32"
                            value={newPrompt.content}
                            onChange={(e) => setNewPrompt(prev => ({ ...prev, content: e.target.value }))}
                          />
                          <div>
                            <label className="text-green-400 text-xs mb-1 block">Category (Type of prompt)</label>
                            {!isAddingCustomCategory ? (
                              <div className="flex space-x-2">
                                <Select
                                  value={newPrompt.category}
                                  onValueChange={(value) => {
                                    if (value === "add_custom") {
                                      setIsAddingCustomCategory(true)
                                      setCustomCategoryInput("")
                                    } else {
                                      setNewPrompt(prev => ({ ...prev, category: value }))
                                    }
                                  }}
                                >
                                  <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300 flex-1">
                                    <SelectValue placeholder="Select category" />
                                  </SelectTrigger>
                                  <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                                    {allCategories.map((category) => (
                                      <SelectItem key={category} value={category} className="text-green-300 hover:bg-gray-700">
                                        {category.charAt(0).toUpperCase() + category.slice(1)}
                                      </SelectItem>
                                    ))}
                                    <SelectItem value="add_custom" className="text-blue-400 hover:bg-gray-700 font-semibold">
                                      + Add Custom Category
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            ) : (
                              <div className="flex space-x-2">
                                <Input
                                  placeholder="Enter custom category..."
                                  className="bg-gray-900 border-gray-600 text-green-300 flex-1"
                                  value={customCategoryInput}
                                  onChange={(e) => setCustomCategoryInput(e.target.value)}
                                  onKeyPress={(e) => {
                                    if (e.key === 'Enter' && customCategoryInput.trim()) {
                                      const newCategory = addCustomCategory(customCategoryInput)
                                      if (newCategory) {
                                        setNewPrompt(prev => ({ ...prev, category: newCategory }))
                                        setIsAddingCustomCategory(false)
                                        setCustomCategoryInput("")
                                      }
                                    }
                                  }}
                                  autoFocus
                                />
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    if (customCategoryInput.trim()) {
                                      const newCategory = addCustomCategory(customCategoryInput)
                                      if (newCategory) {
                                        setNewPrompt(prev => ({ ...prev, category: newCategory }))
                                        setIsAddingCustomCategory(false)
                                        setCustomCategoryInput("")
                                      }
                                    }
                                  }}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  Add
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    setIsAddingCustomCategory(false)
                                    setCustomCategoryInput("")
                                  }}
                                  className="border-gray-600 text-green-300 hover:bg-gray-700"
                                >
                                  Cancel
                                </Button>
                              </div>
                            )}
                          </div>
                          <div>
                            <label className="text-green-400 text-xs mb-1 block">Folder (Organization)</label>
                            <Select
                              value={newPrompt.folderId}
                              onValueChange={(value) => setNewPrompt(prev => ({ ...prev, folderId: value }))}
                            >
                              <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                                <SelectValue placeholder="Select folder (optional)" />
                              </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                              <SelectItem value="none" className="text-green-300 hover:bg-gray-700">No folder</SelectItem>
                              {folders.map((folder) => (
                                <SelectItem
                                  key={folder.id}
                                  value={folder.id}
                                  className="text-green-300 hover:bg-gray-700"
                                >
                                  {folder.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          </div>
                          <Button
                            className="w-full bg-green-600 hover:bg-green-700"
                            onClick={createPrompt}
                            disabled={isCreatingPrompt || !newPrompt.title.trim() || !newPrompt.content.trim()}
                          >
                            {isCreatingPrompt ? "Creating..." : "Create Prompt"}
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Dialog open={isNewFolderDialogOpen} onOpenChange={setIsNewFolderDialogOpen}>
                      <DialogTrigger asChild>
                        <button className="w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded">
                          + New Folder
                        </button>
                      </DialogTrigger>
                      <DialogContent className="bg-gray-800 border-gray-600 text-green-300 rounded-xl">
                        <DialogHeader>
                          <DialogTitle className="text-green-400">Create New Folder</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <Input
                            placeholder="Folder name..."
                            className="bg-gray-900 border-gray-600 text-green-300"
                            value={newFolderName}
                            onChange={(e) => setNewFolderName(e.target.value)}
                          />
                          <Button
                            className="w-full bg-green-600 hover:bg-green-700"
                            onClick={createFolder}
                            disabled={isCreatingFolder || !newFolderName.trim()}
                          >
                            {isCreatingFolder ? "Creating..." : "Create Folder"}
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>

                    {/* Edit Prompt Dialog */}
                    <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                      <DialogContent className="bg-gray-800 border-gray-600 text-green-300 rounded-xl">
                        <DialogHeader>
                          <DialogTitle className="text-green-400">Edit Prompt</DialogTitle>
                        </DialogHeader>
                        {editingPrompt && (
                          <div className="space-y-4">
                            <Input
                              placeholder="Prompt title..."
                              className="bg-gray-900 border-gray-600 text-green-300"
                              value={editingPrompt.title}
                              onChange={(e) => setEditingPrompt(prev => prev ? { ...prev, title: e.target.value } : null)}
                            />
                            <Textarea
                              placeholder="Prompt content..."
                              className="bg-gray-900 border-gray-600 text-green-300 h-32"
                              value={editingPrompt.content}
                              onChange={(e) => setEditingPrompt(prev => prev ? { ...prev, content: e.target.value } : null)}
                            />
                            <div>
                              <label className="text-green-400 text-xs mb-1 block">Category (Type of prompt)</label>
                              <Select
                                value={editingPrompt.category}
                                onValueChange={(value) => setEditingPrompt(prev => prev ? { ...prev, category: value } : null)}
                              >
                                <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                                  {allCategories.map((category) => (
                                    <SelectItem key={category} value={category} className="text-green-300 hover:bg-gray-700">
                                      {category.charAt(0).toUpperCase() + category.slice(1)}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <label className="text-green-400 text-xs mb-1 block">Folder (Organization)</label>
                              <Select
                                value={(editingPrompt as any).currentFolderId || "none"}
                                onValueChange={(value) => setEditingPrompt(prev => prev ? { ...prev, currentFolderId: value } as any : null)}
                              >
                                <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                                  <SelectItem value="none" className="text-green-300 hover:bg-gray-700">No folder</SelectItem>
                                  {folders.map((folder) => (
                                    <SelectItem
                                      key={folder.id}
                                      value={folder.id}
                                      className="text-green-300 hover:bg-gray-700"
                                    >
                                      {folder.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <Button
                              className="w-full bg-green-600 hover:bg-green-700"
                              onClick={updatePrompt}
                            >
                              Update Prompt
                            </Button>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>

                    {/* Settings Dialog */}
                    <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
                      <DialogContent className="bg-gray-800 border-gray-600 text-green-300 max-w-md rounded-xl">
                        <DialogHeader>
                          <DialogTitle className="text-green-400">User Settings</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <label className="text-green-400 text-xs mb-1 block">Display Name</label>
                            <Input
                              placeholder="Your display name..."
                              className="bg-gray-900 border-gray-600 text-green-300"
                              value={userSettings.displayName}
                              onChange={(e) => setUserSettings(prev => ({ ...prev, displayName: e.target.value }))}
                            />
                          </div>

                          <div>
                            <label className="text-green-400 text-xs mb-1 block">Default Category for New Prompts</label>
                            <Select
                              value={userSettings.defaultCategory}
                              onValueChange={(value) => setUserSettings(prev => ({ ...prev, defaultCategory: value }))}
                            >
                              <SelectTrigger className="bg-gray-900 border-gray-600 text-green-300">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                                {allCategories.map((category) => (
                                  <SelectItem key={category} value={category} className="text-green-300 hover:bg-gray-700">
                                    {category.charAt(0).toUpperCase() + category.slice(1)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id="autoSave"
                              checked={userSettings.autoSave}
                              onChange={(e) => setUserSettings(prev => ({ ...prev, autoSave: e.target.checked }))}
                              className="rounded border-gray-600 bg-gray-900 text-green-500 focus:ring-green-500"
                            />
                            <label htmlFor="autoSave" className="text-green-300 text-sm">
                              Auto-save prompts while typing
                            </label>
                          </div>

                          <div className="border-t border-gray-700 pt-4">
                            <h4 className="text-green-400 text-sm font-semibold mb-2">Custom Categories</h4>
                            <div className="space-y-2">
                              <div className="flex space-x-2">
                                <Input
                                  placeholder="Add custom category..."
                                  className="bg-gray-900 border-gray-600 text-green-300 flex-1"
                                  value={newCustomCategory}
                                  onChange={(e) => setNewCustomCategory(e.target.value)}
                                  onKeyPress={(e) => {
                                    if (e.key === 'Enter' && newCustomCategory.trim()) {
                                      const category = newCustomCategory.trim().toLowerCase()
                                      if (!allCategories.includes(category)) {
                                        setUserSettings(prev => ({
                                          ...prev,
                                          customCategories: [...prev.customCategories, category]
                                        }))
                                        setNewCustomCategory("")
                                      }
                                    }
                                  }}
                                />
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    const category = newCustomCategory.trim().toLowerCase()
                                    if (category && !allCategories.includes(category)) {
                                      setUserSettings(prev => ({
                                        ...prev,
                                        customCategories: [...prev.customCategories, category]
                                      }))
                                      setNewCustomCategory("")
                                    }
                                  }}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  Add
                                </Button>
                              </div>

                              {userSettings.customCategories.length > 0 && (
                                <div className="space-y-1">
                                  <div className="text-xs text-green-500">Your custom categories:</div>
                                  <div className="flex flex-wrap gap-1">
                                    {userSettings.customCategories.map((category) => (
                                      <Badge
                                        key={category}
                                        variant="outline"
                                        className="text-xs text-green-400 border-green-500 bg-green-900/20 cursor-pointer hover:bg-red-900/20 hover:border-red-500 hover:text-red-400"
                                        onClick={() => {
                                          setUserSettings(prev => ({
                                            ...prev,
                                            customCategories: prev.customCategories.filter(c => c !== category)
                                          }))
                                        }}
                                      >
                                        {category.charAt(0).toUpperCase() + category.slice(1)} ×
                                      </Badge>
                                    ))}
                                  </div>
                                  <div className="text-xs text-gray-500">Click to remove</div>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="border-t border-gray-700 pt-4">
                            <h4 className="text-green-400 text-sm font-semibold mb-2">Account Information</h4>
                            <div className="space-y-1 text-xs text-green-500">
                              <div>Email: {user?.email}</div>
                              <div>User ID: {user?.id}</div>
                              <div>Member since: {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</div>
                            </div>
                          </div>

                          <div className="flex space-x-2">
                            <Button
                              className="flex-1 bg-green-600 hover:bg-green-700"
                              onClick={() => {
                                // Save settings to localStorage for now
                                localStorage.setItem('promptManagerSettings', JSON.stringify(userSettings))
                                setIsSettingsOpen(false)
                                addToLog("settings saved")
                              }}
                            >
                              Save Settings
                            </Button>
                            <Button
                              variant="outline"
                              className="flex-1 border-gray-600 text-green-300 hover:bg-gray-700"
                              onClick={() => setIsSettingsOpen(false)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>

                    <button className="w-full text-left p-2 text-green-300 text-sm hover:bg-gray-800 rounded">
                      Import/Export
                    </button>
                  </div>
                </div>

                {/* Trash Bin */}
                <div className="mt-6">
                  <h3 className="text-green-400 text-sm font-bold mb-2 terminal-glow">&gt; DELETE</h3>
                  <div
                    className={`flex items-center justify-center p-4 rounded-lg border-2 border-dashed transition-all duration-300 ${
                      dragOverTrash
                        ? 'border-red-500 bg-red-900/30 scale-110'
                        : 'border-gray-600 hover:border-red-400'
                    }`}
                    onDragOver={(e) => {
                      e.preventDefault()
                      e.dataTransfer.dropEffect = 'move'
                      setDragOverTrash(true)
                      setIsTrashOpen(true)
                    }}
                    onDragLeave={() => {
                      setDragOverTrash(false)
                      setIsTrashOpen(false)
                    }}
                    onDrop={(e) => {
                      e.preventDefault()
                      if (draggedPrompt) {
                        deletePrompt(draggedPrompt.id)
                      }
                      setDragOverTrash(false)
                      setIsTrashOpen(false)
                    }}
                  >
                    <div className={`transition-all duration-300 ${
                      isTrashOpen || dragOverTrash ? 'scale-125 text-red-400' : 'text-gray-500'
                    }`}>
                      <Trash2 className={`w-8 h-8 transition-all duration-300 ${
                        isTrashOpen || dragOverTrash ? 'animate-bounce' : ''
                      }`} />
                    </div>
                  </div>
                  <div className="text-center mt-2">
                    <span className={`text-xs transition-colors duration-300 ${
                      dragOverTrash ? 'text-red-400' : 'text-gray-500'
                    }`}>
                      {dragOverTrash ? 'Release to Delete' : 'Drop to Delete'}
                    </span>
                  </div>
                </div>
                </div>
                )}
              </div>
            </div>

            {/* Main Content */}
            <div className={`flex-1 bg-gray-800 p-3 sm:p-6 overflow-y-auto transition-all duration-300 rounded-xl lg:rounded-none lg:rounded-l-xl shadow-lg ${
              sidebarCollapsed ? 'lg:ml-2' : ''
            }`}>
              {/* Header with Add Prompt Button */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-3 sm:space-y-0">
                <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                  <h2 className="text-green-400 text-lg font-bold terminal-glow">
                    {selectedFolder === null
                      ? "All Prompts"
                      : folders.find(f => f.id === selectedFolder)?.name || "Folder"}
                  </h2>
                  <span className="text-green-500 text-sm">
                    ({filteredPrompts.length} prompts)
                  </span>
                  {selectedFolder !== null && (
                    <Button
                      onClick={() => setSelectedFolder(null)}
                      variant="outline"
                      size="sm"
                      className="text-green-400 border-green-600 hover:bg-green-900 w-fit rounded-lg"
                    >
                      Show All
                    </Button>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={testDatabase}
                    variant="outline"
                    className="text-green-400 border-green-600 hover:bg-green-900 text-sm rounded-lg"
                  >
                    Test DB
                  </Button>
                  <Button
                    onClick={() => setIsNewPromptDialogOpen(true)}
                    className="bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg"
                  >
                    + New Prompt
                  </Button>
                </div>
              </div>

              {/* Search and Filter Bar */}
              <div className="flex flex-col lg:flex-row lg:items-center space-y-3 lg:space-y-0 lg:space-x-4 mb-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-500 w-4 h-4" />
                  <Input
                    placeholder="Search prompts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-gray-900 border-gray-600 text-green-300 focus:border-green-500 rounded-lg"
                  />
                </div>
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-full sm:w-48 bg-gray-900 border-gray-600 text-green-300 rounded-lg">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                      <SelectItem value="all" className="text-green-300 hover:bg-gray-700">All Categories</SelectItem>
                      <SelectItem value="general" className="text-green-300 hover:bg-gray-700">General</SelectItem>
                      <SelectItem value="development" className="text-green-300 hover:bg-gray-700">Development</SelectItem>
                      <SelectItem value="communication" className="text-green-300 hover:bg-gray-700">Communication</SelectItem>
                      <SelectItem value="analysis" className="text-green-300 hover:bg-gray-700">Analysis</SelectItem>
                      <SelectItem value="creative" className="text-green-300 hover:bg-gray-700">Creative</SelectItem>
                      {userSettings.customCategories.map((category) => (
                        <SelectItem key={category} value={category} className="text-green-300 hover:bg-gray-700">
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-full sm:w-40 bg-gray-900 border-gray-600 text-green-300 rounded-lg">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-600 text-green-300">
                      <SelectItem value="smart" className="text-green-300 hover:bg-gray-700">Smart Sort</SelectItem>
                      <SelectItem value="rating" className="text-green-300 hover:bg-gray-700">By Rating</SelectItem>
                      <SelectItem value="date" className="text-green-300 hover:bg-gray-700">By Date</SelectItem>
                      <SelectItem value="title" className="text-green-300 hover:bg-gray-700">By Title</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Organized Control Section */}
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 mb-6">
                {/* Left side - Quick Access Folders */}
                <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                  {folders.length > 0 && (
                    <>
                      <span className="text-green-400 text-sm font-semibold">Folders:</span>
                      <div className="flex flex-wrap items-center gap-2">
                        {/* All Folders Icon */}
                        <div
                          className={`flex items-center space-x-1 px-2 py-1 rounded-lg border border-gray-600 hover:border-green-500 cursor-pointer transition-all ${
                            dragOverFolder === 'all' ? 'border-green-400 bg-green-900/20' : 'hover:bg-gray-800/50'
                          } ${selectedFolder === null ? 'bg-green-900/30 border-green-500' : ''}`}
                          onClick={() => setSelectedFolder(null)}
                          onDragOver={(e) => {
                            e.preventDefault()
                            e.dataTransfer.dropEffect = 'move'
                            setDragOverFolder('all')
                          }}
                          onDragLeave={() => setDragOverFolder(null)}
                          onDrop={(e) => {
                            e.preventDefault()
                            if (draggedPrompt) {
                              movePromptToFolder(draggedPrompt.id, null)
                            }
                            setDragOverFolder(null)
                          }}
                        >
                          <div className="w-4 h-4 bg-green-600 rounded flex items-center justify-center">
                            <Folder className="w-3 h-3 text-white" />
                          </div>
                          <span className="text-xs text-green-300 font-medium">All</span>
                          <span className="text-xs text-green-500">({prompts.length})</span>
                        </div>

                        {/* Individual Folder Icons */}
                        {folders.map((folder) => (
                          <div
                            key={folder.id}
                            className={`flex items-center space-x-1 px-2 py-1 rounded-lg border border-gray-600 hover:border-green-500 cursor-pointer transition-all ${
                              dragOverFolder === folder.id ? 'border-green-400 bg-green-900/20' : 'hover:bg-gray-800/50'
                            } ${selectedFolder === folder.id ? 'bg-green-900/30 border-green-500' : ''}`}
                            onClick={() => setSelectedFolder(selectedFolder === folder.id ? null : folder.id)}
                            onDragOver={(e) => {
                              e.preventDefault()
                              e.dataTransfer.dropEffect = 'move'
                              setDragOverFolder(folder.id)
                            }}
                            onDragLeave={() => setDragOverFolder(null)}
                            onDrop={(e) => {
                              e.preventDefault()
                              if (draggedPrompt) {
                                movePromptToFolder(draggedPrompt.id, folder.id)
                              }
                              setDragOverFolder(null)
                            }}
                          >
                            <div className={`w-4 h-4 ${folder.color} rounded flex items-center justify-center`}>
                              <Folder className="w-3 h-3 text-white" />
                            </div>
                            <span className="text-xs text-green-300 font-medium max-w-16 truncate">
                              {folder.name}
                            </span>
                            <span className="text-xs text-green-500">({folder.promptCount || 0})</span>
                          </div>
                        ))}
                      </div>
                    </>
                  )}
                </div>

                {/* Right side - View and Size Controls */}
                <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                  {/* Size Controls */}
                  {viewMode === "grid" && (
                    <div className="flex items-center space-x-2">
                      <span className="text-green-400 text-sm">Size:</span>
                      <div className="flex items-center space-x-1 bg-gray-900 border border-gray-600 rounded-xl p-1">
                        <Button
                          size="sm"
                          variant={gridSize === "small" ? "default" : "ghost"}
                          onClick={() => setGridSize("small")}
                          className={`h-8 px-2 sm:px-3 text-xs rounded-lg ${
                            gridSize === "small"
                              ? "bg-green-600 text-white"
                              : "text-green-400 hover:text-green-300 hover:bg-gray-800"
                          }`}
                        >
                          5 cols
                        </Button>
                        <Button
                          size="sm"
                          variant={gridSize === "medium" ? "default" : "ghost"}
                          onClick={() => setGridSize("medium")}
                          className={`h-8 px-2 sm:px-3 text-xs rounded-lg ${
                            gridSize === "medium"
                              ? "bg-green-600 text-white"
                              : "text-green-400 hover:text-green-300 hover:bg-gray-800"
                          }`}
                        >
                          3 cols
                        </Button>
                        <Button
                          size="sm"
                          variant={gridSize === "large" ? "default" : "ghost"}
                          onClick={() => setGridSize("large")}
                          className={`h-8 px-2 sm:px-3 text-xs rounded-lg ${
                            gridSize === "large"
                              ? "bg-green-600 text-white"
                              : "text-green-400 hover:text-green-300 hover:bg-gray-800"
                          }`}
                        >
                          2 cols
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* View Controls */}
                  <div className="flex items-center space-x-2">
                    <span className="text-green-400 text-sm font-semibold">View:</span>
                    <div className="flex items-center space-x-1 bg-gray-900 border border-gray-600 rounded-xl p-1">
                      <Button
                        size="sm"
                        variant={viewMode === "grid" ? "default" : "ghost"}
                        onClick={() => setViewMode("grid")}
                        className={`h-8 px-2 sm:px-3 rounded-lg ${
                          viewMode === "grid"
                            ? "bg-green-600 text-white"
                            : "text-green-400 hover:text-green-300 hover:bg-gray-800"
                        }`}
                      >
                        <LayoutGrid className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant={viewMode === "compact" ? "default" : "ghost"}
                        onClick={() => setViewMode("compact")}
                        className={`h-8 px-2 sm:px-3 rounded-lg ${
                          viewMode === "compact"
                            ? "bg-green-600 text-white"
                            : "text-green-400 hover:text-green-300 hover:bg-gray-800"
                        }`}
                      >
                        <Grid3X3 className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant={viewMode === "list" ? "default" : "ghost"}
                        onClick={() => setViewMode("list")}
                        className={`h-8 px-2 sm:px-3 rounded-lg ${
                          viewMode === "list"
                            ? "bg-green-600 text-white"
                            : "text-green-400 hover:text-green-300 hover:bg-gray-800"
                        }`}
                      >
                        <List className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Prompts Display */}
              {viewMode === "list" ? (
                // List View
                <div className="space-y-2">
                  {loadingPrompts ? (
                    <div className="text-center text-green-400 py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4"></div>
                      Loading prompts...
                    </div>
                  ) : filteredPrompts.length === 0 ? (
                    <div className="text-center text-green-400 py-8">
                      {selectedFolder !== null
                        ? `No prompts in "${folders.find(f => f.id === selectedFolder)?.name}" folder yet.`
                        : searchTerm || selectedCategory !== "all"
                          ? "No prompts match your search."
                          : "No prompts yet. Create your first prompt!"
                      }
                      <div className="mt-4">
                        <Button
                          onClick={() => setIsNewPromptDialogOpen(true)}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          + Create First Prompt
                        </Button>
                      </div>
                    </div>
                  ) : (
                    filteredPrompts.map((prompt) => (
                      <div
                        key={prompt.id}
                        draggable
                        onDragStart={(e) => {
                          setDraggedPrompt(prompt)
                          e.dataTransfer.effectAllowed = 'move'
                          e.dataTransfer.setData('text/plain', prompt.id)
                          e.currentTarget.style.opacity = '0.5'
                        }}
                        onDragEnd={(e) => {
                          setDraggedPrompt(null)
                          setDragOverFolder(null)
                          setDragOverTrash(false)
                          setIsTrashOpen(false)
                          e.currentTarget.style.opacity = '1'
                        }}
                        className={`bg-gray-900 border border-gray-700 rounded-xl p-4 hover:border-green-500 transition-colors cursor-move flex items-center justify-between ${
                          prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-4 flex-1">
                          <div className="flex items-center space-x-2">
                            {prompt.is_pinned && <Pin className="w-4 h-4 text-green-500" />}
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`w-3 h-3 cursor-pointer transition-colors hover:text-green-300 ${
                                    i < prompt.rating ? "text-green-400 fill-current" : "text-gray-600 hover:text-gray-400"
                                  }`}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    const newRating = i + 1
                                    if (newRating === prompt.rating) {
                                      updateRating(prompt.id, 0)
                                    } else {
                                      updateRating(prompt.id, newRating)
                                    }
                                  }}
                                />
                              ))}
                            </div>
                          </div>
                          <div className="flex-1">
                            <h3 className="text-green-400 font-semibold text-sm">{prompt.title}</h3>
                            <p className="text-green-300 text-xs mt-1 line-clamp-1">{prompt.content}</p>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            <Badge variant="outline" className="text-xs text-blue-400 border-blue-500 bg-blue-900/20">
                              {prompt.category.toUpperCase()}
                            </Badge>
                            {(prompt as any).folderInfo && (
                              <Badge variant="outline" className="text-xs text-purple-400 border-purple-500 bg-purple-900/20">
                                {(prompt as any).folderInfo.name}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyPrompt(prompt)}
                            className="h-8 w-8 p-0 text-green-400 hover:text-green-300 hover:bg-gray-800"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              setEditingPrompt(prompt)
                              setIsEditDialogOpen(true)
                            }}
                            className="h-8 w-8 p-0 text-green-400 hover:text-green-300 hover:bg-gray-800"
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              ) : (
                // Grid Views (both normal grid and compact)
                <div className={`grid gap-3 sm:gap-4 ${
                  viewMode === "compact" 
                    ? "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6"
                    : gridSize === "small"
                      ? "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
                      : gridSize === "medium"
                        ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                        : "grid-cols-1 lg:grid-cols-2"
                }`}>
                  {loadingPrompts ? (
                    <div className="col-span-full text-center text-green-400 py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4"></div>
                      Loading prompts...
                    </div>
                  ) : filteredPrompts.length === 0 ? (
                    <div className="col-span-full text-center text-green-400 py-8">
                      {selectedFolder !== null
                        ? `No prompts in "${folders.find(f => f.id === selectedFolder)?.name}" folder yet.`
                        : searchTerm || selectedCategory !== "all"
                          ? "No prompts match your search."
                          : "No prompts yet. Create your first prompt!"
                      }
                      <div className="mt-4">
                        <Button
                          onClick={() => setIsNewPromptDialogOpen(true)}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          + Create First Prompt
                        </Button>
                      </div>
                    </div>
                  ) : viewMode === "compact" ? (
                    // Compact Cards (Title Only)
                    filteredPrompts.map((prompt) => (
                      <Card
                        key={prompt.id}
                        draggable
                        onDragStart={(e) => {
                          setDraggedPrompt(prompt)
                          e.dataTransfer.effectAllowed = 'move'
                          e.dataTransfer.setData('text/plain', prompt.id)
                          e.currentTarget.style.opacity = '0.5'
                        }}
                        onDragEnd={(e) => {
                          setDraggedPrompt(null)
                          setDragOverFolder(null)
                          setDragOverTrash(false)
                          setIsTrashOpen(false)
                          e.currentTarget.style.opacity = '1'
                        }}
                        className={`bg-gray-900 border-gray-700 hover:border-green-500 transition-colors cursor-move rounded-xl ${
                          prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''
                        }`}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-1">
                              {prompt.is_pinned && <Pin className="w-3 h-3 text-green-500" />}
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`w-2 h-2 cursor-pointer transition-colors hover:text-green-300 ${
                                      i < prompt.rating ? "text-green-400 fill-current" : "text-gray-600 hover:text-gray-400"
                                    }`}
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      const newRating = i + 1
                                      if (newRating === prompt.rating) {
                                        updateRating(prompt.id, 0)
                                      } else {
                                        updateRating(prompt.id, newRating)
                                      }
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                          </div>
                          <h3 className="text-green-400 text-xs font-bold mb-2 line-clamp-2 min-h-[2rem]">{prompt.title}</h3>
                          <div className="flex items-center justify-between">
                            <Badge variant="outline" className="text-xs text-blue-400 border-blue-500 bg-blue-900/20">
                              {prompt.category.charAt(0).toUpperCase()}
                            </Badge>
                            <div className="flex items-center space-x-1">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => copyPrompt(prompt)}
                                className="h-6 w-6 p-0 text-green-400 hover:text-green-300 hover:bg-gray-800"
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  setEditingPrompt(prompt)
                                  setIsEditDialogOpen(true)
                                }}
                                className="h-6 w-6 p-0 text-green-400 hover:text-green-300 hover:bg-gray-800"
                              >
                                <Edit className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    // Full Grid Cards
                    filteredPrompts.map((prompt) => (
                    <Card
                      key={prompt.id}
                      draggable
                      onDragStart={(e) => {
                        setDraggedPrompt(prompt)
                        e.dataTransfer.effectAllowed = 'move'
                        e.dataTransfer.setData('text/plain', prompt.id)
                        e.currentTarget.style.opacity = '0.5'
                      }}
                      onDragEnd={(e) => {
                        setDraggedPrompt(null)
                        setDragOverFolder(null)
                        setDragOverTrash(false)
                        setIsTrashOpen(false)
                        e.currentTarget.style.opacity = '1'
                      }}
                      className={`bg-gray-900 border-gray-700 hover:border-green-500 transition-colors cursor-move rounded-xl ${
                        prompt.is_pinned ? 'ring-1 ring-green-500/30 bg-green-900/10' : ''
                      }`}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-start justify-between">
                          <div className="absolute top-2 right-2 opacity-30 hover:opacity-60 transition-opacity">
                            <div className="text-green-400 text-xs">⋮⋮</div>
                          </div>
                          <CardTitle className="text-green-400 text-sm font-bold">{prompt.title}</CardTitle>
                          <div className="flex items-center space-x-1">
                            {prompt.is_pinned && <Pin className="w-3 h-3 text-green-500" />}
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`w-3 h-3 cursor-pointer transition-colors hover:text-green-300 ${
                                    i < prompt.rating ? "text-green-400 fill-current" : "text-gray-600 hover:text-gray-400"
                                  }`}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    const newRating = i + 1
                                    if (newRating === prompt.rating) {
                                      updateRating(prompt.id, 0)
                                    } else {
                                      updateRating(prompt.id, newRating)
                                    }
                                  }}
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          <Badge variant="outline" className="text-xs text-blue-400 border-blue-500 bg-blue-900/20">
                            {prompt.category.toUpperCase()}
                          </Badge>
                          {(prompt as any).folderInfo && (
                            <Badge variant="outline" className="text-xs text-purple-400 border-purple-500 bg-purple-900/20">
                              {(prompt as any).folderInfo.name}
                            </Badge>
                          )}
                          {prompt.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs text-green-500 border-green-600 bg-green-900/20">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-green-300 text-xs mb-3 line-clamp-3">{prompt.content}</p>
                        <div className="flex items-center justify-between text-xs text-green-500">
                          <span>{new Date(prompt.created_at).toLocaleDateString()}</span>
                          {prompt.last_used && <span>Used: {new Date(prompt.last_used).toLocaleDateString()}</span>}
                        </div>
                        <div className="flex items-center space-x-2 mt-3">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyPrompt(prompt)}
                            className="text-green-400 border-green-600 hover:bg-green-900"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => togglePin(prompt.id)}
                            className="text-green-400 border-green-600 hover:bg-green-900"
                          >
                            <Pin className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setEditingPrompt(prompt)
                              setIsEditDialogOpen(true)
                            }}
                            className="text-green-400 border-green-600 hover:bg-green-900"
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                    ))
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Status Bar */}
          <div className="bg-gray-900 px-6 py-2 border-t border-gray-700">
            <div className="flex items-center justify-between text-green-500 text-xs">
              <div className="flex items-center space-x-4">
                {draggedPrompt ? (
                  <>
                    <span className="text-blue-400">Dragging "{draggedPrompt.title}"</span>
                    <span>|</span>
                    <span className="text-blue-400">Drop on folder to move</span>
                    <span>|</span>
                    <span className="text-red-400">Drop on trash to delete</span>
                  </>
                ) : (
                  <>
                    <span>Ready</span>
                    <span>|</span>
                    <span>{filteredPrompts.length} prompts</span>
                    <span>|</span>
                    <span>{folders.length} folders</span>
                  </>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-3 h-3" />
                <span>Last activity: {activityLog[activityLog.length - 1]}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
