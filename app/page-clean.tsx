"use client"

import { useState, useEffect, useMemo } from "react"
import { useRouter } from "next/navigation"
import { Search, Star, Copy, Edit, Pin, Clock, Terminal, LogOut, Folder, Trash2, Grid3X3, Grid2X2, LayoutGrid, List } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { promptService, folderService, promptFolderService, subscriptions } from "@/lib/database"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Prompt {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  rating: number
  is_pinned: boolean
  created_at: string
  updated_at: string
  last_used: string | null
  user_id: string
}

interface FolderType {
  id: string
  name: string
  color: string
  created_at: string
  updated_at: string
  user_id: string
  promptCount?: number
}

export default function TerminalPromptManager() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()

  // State declarations
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [folders, setFolders] = useState<FolderType[]>([])
  const [loadingPrompts, setLoadingPrompts] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [sortBy, setSortBy] = useState("smart")
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [layoutMode, setLayoutMode] = useState<'large' | 'medium' | 'small' | 'list'>('medium')
  
  // Dialog states
  const [isNewPromptDialogOpen, setIsNewPromptDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isNewFolderDialogOpen, setIsNewFolderDialogOpen] = useState(false)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  
  // Form states
  const [newPrompt, setNewPrompt] = useState({ title: "", content: "", category: "general", tags: "", folder: "" })
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null)
  const [newFolder, setNewFolder] = useState({ name: "", color: "bg-blue-600" })
  
  // Drag and drop states
  const [draggedPrompt, setDraggedPrompt] = useState<Prompt | null>(null)
  const [dragOverFolder, setDragOverFolder] = useState<string | null>(null)
  const [dragOverTrash, setDragOverTrash] = useState(false)
  const [isTrashOpen, setIsTrashOpen] = useState(false)
  
  // Activity and settings
  const [activityLog, setActivityLog] = useState<string[]>(["System initialized"])
  const [userSettings, setUserSettings] = useState({
    customCategories: [] as string[],
    defaultSort: "smart" as string,
    defaultLayout: "medium" as string
  })
  const [newCustomCategory, setNewCustomCategory] = useState("")

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 p-8 text-center">
          <div className="text-green-400 text-lg mb-4">Loading...</div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"></div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!user) {
    return null
  }

  // Helper functions
  const addToLog = (message: string) => {
    setActivityLog(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const allCategories = [
    "general", "development", "communication", "analysis", "creative",
    ...userSettings.customCategories
  ]

  // Load data on component mount
  useEffect(() => {
    if (user) {
      loadPrompts()
      loadFolders()
      loadSettings()
    }
  }, [user])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user) return

    const promptsSubscription = subscriptions.prompts(user.id, (payload) => {
      if (payload.eventType === 'INSERT') {
        setPrompts(prev => [...prev, payload.new as Prompt])
        addToLog("new prompt added")
      } else if (payload.eventType === 'UPDATE') {
        setPrompts(prev => prev.map(p => p.id === payload.new.id ? payload.new as Prompt : p))
        addToLog("prompt updated")
      } else if (payload.eventType === 'DELETE') {
        setPrompts(prev => prev.filter(p => p.id !== payload.old.id))
        addToLog("prompt deleted")
      }
    })

    const foldersSubscription = subscriptions.folders(user.id, (payload) => {
      if (payload.eventType === 'INSERT') {
        setFolders(prev => [...prev, payload.new as FolderType])
        addToLog("new folder created")
      } else if (payload.eventType === 'UPDATE') {
        setFolders(prev => prev.map(f => f.id === payload.new.id ? payload.new as FolderType : f))
        addToLog("folder updated")
      } else if (payload.eventType === 'DELETE') {
        setFolders(prev => prev.filter(f => f.id !== payload.old.id))
        addToLog("folder deleted")
      }
    })

    return () => {
      promptsSubscription?.unsubscribe()
      foldersSubscription?.unsubscribe()
    }
  }, [user])

  const loadSettings = () => {
    try {
      const saved = localStorage.getItem('promptManagerSettings')
      if (saved) {
        const settings = JSON.parse(saved)
        setUserSettings(settings)
        setSortBy(settings.defaultSort || "smart")
        setLayoutMode(settings.defaultLayout || "medium")
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const loadPrompts = async () => {
    if (!user) return
    
    try {
      setLoadingPrompts(true)
      const data = await promptService.getAll(user.id)
      setPrompts(data)
      addToLog(`loaded ${data.length} prompts`)
    } catch (error) {
      console.error('Error loading prompts:', error)
      addToLog("error loading prompts")
    } finally {
      setLoadingPrompts(false)
    }
  }

  const loadFolders = async () => {
    if (!user) return
    
    try {
      const data = await folderService.getAll(user.id)
      
      // Get prompt counts for each folder
      const foldersWithCounts = await Promise.all(
        data.map(async (folder) => {
          const promptCount = await promptFolderService.getPromptCountByFolder(folder.id)
          return { ...folder, promptCount }
        })
      )
      
      setFolders(foldersWithCounts)
      addToLog(`loaded ${data.length} folders`)
    } catch (error) {
      console.error('Error loading folders:', error)
      addToLog("error loading folders")
    }
  }

  // Database test function
  const testDatabase = async () => {
    try {
      addToLog("testing database connection...")
      await promptService.getAll(user!.id)
      addToLog("database connection successful")
    } catch (error) {
      console.error('Database test failed:', error)
      addToLog("database connection failed")
    }
  }

  // CRUD Functions
  const createPrompt = async () => {
    if (!user || !newPrompt.title.trim() || !newPrompt.content.trim()) return

    try {
      const promptData = {
        title: newPrompt.title.trim(),
        content: newPrompt.content.trim(),
        category: newPrompt.category,
        tags: newPrompt.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        rating: 0,
        is_pinned: false,
        user_id: user.id
      }

      const createdPrompt = await promptService.create(promptData)

      // Add to folder if specified
      if (newPrompt.folder) {
        await promptFolderService.addPromptToFolder(createdPrompt.id, newPrompt.folder)
      }

      setNewPrompt({ title: "", content: "", category: "general", tags: "", folder: "" })
      setIsNewPromptDialogOpen(false)
      addToLog("prompt created successfully")
    } catch (error) {
      console.error('Error creating prompt:', error)
      addToLog("error creating prompt")
    }
  }

  const updatePrompt = async () => {
    if (!user || !editingPrompt) return

    try {
      const updatedData = {
        title: editingPrompt.title.trim(),
        content: editingPrompt.content.trim(),
        category: editingPrompt.category,
        tags: editingPrompt.tags,
        updated_at: new Date().toISOString()
      }

      await promptService.update(editingPrompt.id, updatedData, user.id)
      setEditingPrompt(null)
      setIsEditDialogOpen(false)
      addToLog("prompt updated successfully")
    } catch (error) {
      console.error('Error updating prompt:', error)
      addToLog("error updating prompt")
    }
  }

  const deletePrompt = async (id: string) => {
    if (!user) return

    try {
      await promptService.delete(id, user.id)
      addToLog("prompt deleted successfully")
    } catch (error) {
      console.error('Error deleting prompt:', error)
      addToLog("error deleting prompt")
    }
  }

  const updateRating = async (id: string, rating: number) => {
    if (!user) return

    try {
      await promptService.update(id, { rating }, user.id)
      addToLog(`rating updated to ${rating} stars`)
    } catch (error) {
      console.error('Error updating rating:', error)
      addToLog("error updating rating")
    }
  }

  const togglePin = async (id: string) => {
    if (!user) return

    try {
      await promptService.togglePin(id, user.id)
      addToLog("pin status toggled")
    } catch (error) {
      console.error('Error toggling pin:', error)
      addToLog("error toggling pin")
    }
  }

  const copyPrompt = async (prompt: Prompt) => {
    try {
      await navigator.clipboard.writeText(prompt.content)
      await promptService.updateLastUsed(prompt.id, user!.id)
      addToLog("prompt copied to clipboard")
    } catch (error) {
      console.error('Error copying prompt:', error)
      addToLog("error copying prompt")
    }
  }

  const openEditDialog = (prompt: Prompt) => {
    setEditingPrompt(prompt)
    setIsEditDialogOpen(true)
  }

  // Folder functions
  const createFolder = async () => {
    if (!user || !newFolder.name.trim()) return

    try {
      await folderService.create({
        name: newFolder.name.trim(),
        color: newFolder.color,
        user_id: user.id
      })

      setNewFolder({ name: "", color: "bg-blue-600" })
      setIsNewFolderDialogOpen(false)
      addToLog("folder created successfully")
    } catch (error) {
      console.error('Error creating folder:', error)
      addToLog("error creating folder")
    }
  }

  const movePromptToFolder = async (promptId: string, folderId: string | null) => {
    if (!user) return

    try {
      // Remove from current folder first
      await promptFolderService.removePromptFromAllFolders(promptId)

      // Add to new folder if specified
      if (folderId) {
        await promptFolderService.addPromptToFolder(promptId, folderId)
      }

      // Reload folders to update counts
      loadFolders()
      addToLog("prompt moved successfully")
    } catch (error) {
      console.error('Error moving prompt:', error)
      addToLog("error moving prompt")
    }
  }

  // Filtering and sorting logic
  const promptsWithFolderInfo = useMemo(() => {
    const filteredPrompts = prompts
      .filter(prompt => {
        // Search filter
        const matchesSearch = searchTerm === "" ||
          prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          prompt.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
          prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))

        // Category filter
        const matchesCategory = selectedCategory === "all" || prompt.category === selectedCategory

        // Folder filter
        let matchesFolder = true
        if (selectedFolder !== null) {
          // This would need to be enhanced with actual folder-prompt relationships
          // For now, we'll show all prompts when a folder is selected
          matchesFolder = true
        }

        return matchesSearch && matchesCategory && matchesFolder
      })
      .sort((a, b) => {
        if (sortBy === "smart") {
          // Smart sort: pinned first, then by rating, then by last used, then by date
          if (a.is_pinned !== b.is_pinned) {
            return a.is_pinned ? -1 : 1
          }
          if (a.rating !== b.rating) {
            return b.rating - a.rating
          }
          if (a.last_used && b.last_used) {
            return new Date(b.last_used).getTime() - new Date(a.last_used).getTime()
          }
          if (a.last_used && !b.last_used) return -1
          if (!a.last_used && b.last_used) return 1
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        } else if (sortBy === "rating") {
          if (a.is_pinned !== b.is_pinned) {
            return a.is_pinned ? -1 : 1
          }
          return b.rating - a.rating
        } else if (sortBy === "date") {
          if (a.is_pinned !== b.is_pinned) {
            return a.is_pinned ? -1 : 1
          }
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        } else if (sortBy === "title") {
          if (a.is_pinned !== b.is_pinned) {
            return a.is_pinned ? -1 : 1
          }
          return a.title.localeCompare(b.title)
        }
        return 0
      })

    // Add folder information to prompts for display
    return filteredPrompts.map(prompt => {
      // This would be enhanced with actual folder relationships
      return { ...prompt, folderInfo: null }
    })
  }, [prompts, searchTerm, selectedCategory, selectedFolder, sortBy])

  return (
    <div className="min-h-screen bg-gray-900 p-0">
      <div className="w-full h-screen">
        <div className="bg-gray-800 h-screen overflow-hidden">
          {/* Header */}
          <div className="bg-gray-700 px-6 py-3 border-b border-gray-600">
            <div className="flex items-center justify-between">
              <div className="text-green-400 font-bold text-lg">PROMPT MANAGER</div>
              <div className="flex items-center space-x-4">
                <div className="text-green-400 text-sm">{user?.email}</div>
                <Button
                  onClick={signOut}
                  variant="outline"
                  size="sm"
                  className="text-green-400 border-green-600 hover:bg-green-900"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </Button>
              </div>
            </div>
          </div>

          {/* Simple Content for Testing */}
          <div className="p-6">
            <div className="text-green-400 text-lg mb-4">Welcome to Prompt Manager!</div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {promptsWithFolderInfo.map((prompt) => (
                <Card key={prompt.id} className="bg-gray-900 border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-green-400">{prompt.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-green-300 text-sm">{prompt.content}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="mt-4">
              <Button
                onClick={testDatabase}
                variant="outline"
                className="text-green-400 border-green-600 hover:bg-green-900"
              >
                Test Database Connection
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
