import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client (for API routes)
export const createServerClient = () => {
  return createClient(
    supabaseUrl,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )
}

// Database types
export interface Database {
  public: {
    Tables: {
      prompts: {
        Row: {
          id: string
          title: string
          content: string
          category: string
          tags: string[]
          rating: number
          is_pinned: boolean
          created_at: string
          updated_at: string
          last_used: string | null
          user_id: string
        }
        Insert: {
          id?: string
          title: string
          content: string
          category: string
          tags?: string[]
          rating?: number
          is_pinned?: boolean
          created_at?: string
          updated_at?: string
          last_used?: string | null
          user_id: string
        }
        Update: {
          id?: string
          title?: string
          content?: string
          category?: string
          tags?: string[]
          rating?: number
          is_pinned?: boolean
          created_at?: string
          updated_at?: string
          last_used?: string | null
          user_id?: string
        }
      }
      folders: {
        Row: {
          id: string
          name: string
          color: string
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          name: string
          color: string
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          name?: string
          color?: string
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      prompt_folders: {
        Row: {
          id: string
          prompt_id: string
          folder_id: string
          created_at: string
        }
        Insert: {
          id?: string
          prompt_id: string
          folder_id: string
          created_at?: string
        }
        Update: {
          id?: string
          prompt_id?: string
          folder_id?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

export type Prompt = Database['public']['Tables']['prompts']['Row']
export type Folder = Database['public']['Tables']['folders']['Row']
export type PromptFolder = Database['public']['Tables']['prompt_folders']['Row']
