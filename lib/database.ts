import { supabase } from './supabase'
import type { Database, Prompt, Folder } from './supabase'

// Prompt operations
export const promptService = {
  // Get all prompts for the current user
  async getAll(userId: string) {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Get a single prompt by ID
  async getById(id: string, userId: string) {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('id', id)
      .eq('user_id', userId)
      .single()

    if (error) throw error
    return data
  },

  // Create a new prompt
  async create(prompt: Database['public']['Tables']['prompts']['Insert']) {
    const { data, error } = await supabase
      .from('prompts')
      .insert(prompt)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update a prompt
  async update(id: string, updates: Database['public']['Tables']['prompts']['Update'], userId: string) {
    const { data, error } = await supabase
      .from('prompts')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete a prompt
  async delete(id: string, userId: string) {
    const { error } = await supabase
      .from('prompts')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) throw error
  },

  // Update last used timestamp
  async updateLastUsed(id: string, userId: string) {
    const { error } = await supabase
      .from('prompts')
      .update({ last_used: new Date().toISOString() })
      .eq('id', id)
      .eq('user_id', userId)

    if (error) throw error
  },

  // Toggle pin status
  async togglePin(id: string, userId: string) {
    // First get the current pin status
    const { data: prompt, error: fetchError } = await supabase
      .from('prompts')
      .select('is_pinned')
      .eq('id', id)
      .eq('user_id', userId)
      .single()

    if (fetchError) throw fetchError

    // Toggle the pin status
    const { data, error } = await supabase
      .from('prompts')
      .update({ is_pinned: !prompt.is_pinned })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Search prompts
  async search(query: string, userId: string, category?: string) {
    let queryBuilder = supabase
      .from('prompts')
      .select('*')
      .eq('user_id', userId)

    if (category && category !== 'all') {
      queryBuilder = queryBuilder.eq('category', category)
    }

    // Use text search for title and content
    queryBuilder = queryBuilder.or(`title.ilike.%${query}%,content.ilike.%${query}%`)

    const { data, error } = await queryBuilder.order('created_at', { ascending: false })

    if (error) throw error
    return data
  }
}

// Folder operations
export const folderService = {
  // Get all folders for the current user
  async getAll(userId: string) {
    const { data, error } = await supabase
      .from('folders')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Create a new folder
  async create(folder: Database['public']['Tables']['folders']['Insert']) {
    const { data, error } = await supabase
      .from('folders')
      .insert(folder)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update a folder
  async update(id: string, updates: Database['public']['Tables']['folders']['Update'], userId: string) {
    const { data, error } = await supabase
      .from('folders')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete a folder
  async delete(id: string, userId: string) {
    const { error } = await supabase
      .from('folders')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) throw error
  },

  // Get folder with prompt count
  async getWithPromptCount(userId: string) {
    const { data, error } = await supabase
      .from('folders')
      .select(`
        *,
        prompt_folders(count)
      `)
      .eq('user_id', userId)

    if (error) throw error
    return data
  }
}

// Prompt-Folder relationship operations
export const promptFolderService = {
  // Add prompt to folder
  async addPromptToFolder(promptId: string, folderId: string) {
    const { data, error } = await supabase
      .from('prompt_folders')
      .insert({ prompt_id: promptId, folder_id: folderId })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Remove prompt from folder
  async removePromptFromFolder(promptId: string, folderId: string) {
    const { error } = await supabase
      .from('prompt_folders')
      .delete()
      .eq('prompt_id', promptId)
      .eq('folder_id', folderId)

    if (error) throw error
  },

  // Get all prompts in a folder
  async getPromptsInFolder(folderId: string, userId: string) {
    const { data, error } = await supabase
      .from('prompt_folders')
      .select(`
        prompt_id,
        prompts!inner(*)
      `)
      .eq('folder_id', folderId)
      .eq('prompts.user_id', userId)

    if (error) throw error
    return data?.map(item => item.prompts) || []
  },

  // Get folder for a prompt
  async getFolderForPrompt(promptId: string) {
    const { data, error } = await supabase
      .from('prompt_folders')
      .select(`
        folder_id,
        folders(*)
      `)
      .eq('prompt_id', promptId)
      .single()

    if (error && error.code !== 'PGRST116') throw error // PGRST116 = no rows returned
    return data?.folders || null
  },

  // Update prompt folder (move to different folder)
  async updatePromptFolder(promptId: string, oldFolderId: string | null, newFolderId: string | null) {
    // Remove from old folder if exists
    if (oldFolderId) {
      await this.removePromptFromFolder(promptId, oldFolderId)
    }

    // Add to new folder if specified
    if (newFolderId) {
      await this.addPromptToFolder(promptId, newFolderId)
    }
  }
}

// Real-time subscriptions
export const subscriptions = {
  // Subscribe to prompt changes
  subscribeToPrompts(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('prompts')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'prompts',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe()
  },

  // Subscribe to folder changes
  subscribeToFolders(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('folders')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'folders',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe()
  }
}
